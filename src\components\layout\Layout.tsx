import React from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { Footer } from './Footer';
import { Breadcrumb } from '@/components/Breadcrumb';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import { cn } from '@/utils/cn';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
  showSidebar?: boolean;
  showFooter?: boolean;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  className,
  showSidebar = true,
  showFooter = true,
}) => {
  // const { sidebarOpen } = useAppStore();

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="flex">
        {showSidebar && <Sidebar />}

        <main
          className={cn(
            'flex-1 transition-all duration-200 ease-in-out',
            showSidebar && 'md:ml-0', // Sidebar is positioned relative on md+
            className
          )}
        >
          <div className="container mx-auto px-4 py-6">
            <div className="mb-4">
              <Breadcrumb />
            </div>
            <ErrorBoundary>
              {children}
            </ErrorBoundary>
          </div>
        </main>
      </div>

      {showFooter && <Footer />}
    </div>
  );
};
