// Modern Theme Configuration for Japanese Learning Platform
export const themeConfig = {
  // Color System - Japanese-inspired palette
  colors: {
    // Primary: Japanese Red (Aka)
    primary: {
      50: '#fef2f2',
      100: '#fee2e2',
      200: '#fecaca',
      300: '#fca5a5',
      400: '#f87171',
      500: '#ef4444', // Main red
      600: '#dc2626',
      700: '#b91c1c',
      800: '#991b1b',
      900: '#7f1d1d',
      950: '#450a0a',
    },
    
    // Secondary: Ocean Blue (Umi)
    secondary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9', // Main blue
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49',
    },
    
    // Tertiary: <PERSON><PERSON> (<PERSON>a)
    tertiary: {
      50: '#f0fdf4',
      100: '#dcfce7',
      200: '#bbf7d0',
      300: '#86efac',
      400: '#4ade80',
      500: '#22c55e', // Main green
      600: '#16a34a',
      700: '#15803d',
      800: '#166534',
      900: '#14532d',
      950: '#052e16',
    },
    
    // Neutral grays with warm undertones
    neutral: {
      50: '#fafaf9',
      100: '#f5f5f4',
      200: '#e7e5e4',
      300: '#d6d3d1',
      400: '#a8a29e',
      500: '#78716c',
      600: '#57534e',
      700: '#44403c',
      800: '#292524',
      900: '#1c1917',
      950: '#0c0a09',
    },
  },
  
  // Typography Scale
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      japanese: ['Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', 'sans-serif'],
      mono: ['JetBrains Mono', 'Fira Code', 'monospace'],
    },
    fontSize: {
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '1.75rem' }],
      '2xl': ['1.5rem', { lineHeight: '2rem' }],
      '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
      '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      '5xl': ['3rem', { lineHeight: '1' }],
      '6xl': ['3.75rem', { lineHeight: '1' }],
    },
  },
  
  // Spacing Scale
  spacing: {
    px: '1px',
    0: '0',
    0.5: '0.125rem',
    1: '0.25rem',
    1.5: '0.375rem',
    2: '0.5rem',
    2.5: '0.625rem',
    3: '0.75rem',
    3.5: '0.875rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    7: '1.75rem',
    8: '2rem',
    9: '2.25rem',
    10: '2.5rem',
    11: '2.75rem',
    12: '3rem',
    14: '3.5rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
  },
  
  // Border Radius
  borderRadius: {
    none: '0',
    sm: '0.125rem',
    DEFAULT: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },
  
  // Shadows - Soft, modern shadows
  boxShadow: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    DEFAULT: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
    '2xl': '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    inner: 'inset 0 2px 4px 0 rgb(0 0 0 / 0.05)',
    none: 'none',
    
    // Glassmorphism shadows
    glass: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
    'glass-lg': '0 8px 32px 0 rgba(31, 38, 135, 0.5)',
    
    // Neumorphism shadows
    neu: '8px 8px 16px #d1d9e6, -8px -8px 16px #ffffff',
    'neu-inset': 'inset 8px 8px 16px #d1d9e6, inset -8px -8px 16px #ffffff',
  },
  
  // Animation & Transitions
  animation: {
    // Entrance animations
    'fade-in': 'fadeIn 0.5s ease-out',
    'slide-up': 'slideUp 0.5s ease-out',
    'slide-down': 'slideDown 0.5s ease-out',
    'slide-left': 'slideLeft 0.5s ease-out',
    'slide-right': 'slideRight 0.5s ease-out',
    'scale-in': 'scaleIn 0.3s ease-out',
    'bounce-in': 'bounceIn 0.6s ease-out',
    
    // Progress animations
    'progress-fill': 'progressFill 1s ease-out',
    'shimmer': 'shimmer 2s linear infinite',
    
    // Hover animations
    'hover-lift': 'hoverLift 0.2s ease-out',
    'hover-glow': 'hoverGlow 0.3s ease-out',
  },
  
  // Transition timing
  transitionDuration: {
    75: '75ms',
    100: '100ms',
    150: '150ms',
    200: '200ms',
    300: '300ms',
    500: '500ms',
    700: '700ms',
    1000: '1000ms',
  },
  
  // Glassmorphism utilities
  backdrop: {
    blur: {
      xs: 'blur(2px)',
      sm: 'blur(4px)',
      DEFAULT: 'blur(8px)',
      md: 'blur(12px)',
      lg: 'blur(16px)',
      xl: 'blur(24px)',
      '2xl': 'blur(40px)',
      '3xl': 'blur(64px)',
    },
  },
} as const;

// CSS Custom Properties for dynamic theming
export const cssVariables = {
  light: {
    '--color-background': '255 255 255',
    '--color-foreground': '28 25 23',
    '--color-card': '255 255 255',
    '--color-card-foreground': '28 25 23',
    '--color-popover': '255 255 255',
    '--color-popover-foreground': '28 25 23',
    '--color-primary': '239 68 68',
    '--color-primary-foreground': '255 255 255',
    '--color-secondary': '14 165 233',
    '--color-secondary-foreground': '255 255 255',
    '--color-tertiary': '34 197 94',
    '--color-tertiary-foreground': '255 255 255',
    '--color-muted': '245 245 244',
    '--color-muted-foreground': '120 113 108',
    '--color-accent': '245 245 244',
    '--color-accent-foreground': '28 25 23',
    '--color-destructive': '239 68 68',
    '--color-destructive-foreground': '255 255 255',
    '--color-border': '231 229 228',
    '--color-input': '231 229 228',
    '--color-ring': '239 68 68',
    '--radius': '1rem',
  },
  dark: {
    '--color-background': '12 10 9',
    '--color-foreground': '250 250 249',
    '--color-card': '28 25 23',
    '--color-card-foreground': '250 250 249',
    '--color-popover': '28 25 23',
    '--color-popover-foreground': '250 250 249',
    '--color-primary': '248 113 113',
    '--color-primary-foreground': '28 25 23',
    '--color-secondary': '56 189 248',
    '--color-secondary-foreground': '28 25 23',
    '--color-tertiary': '74 222 128',
    '--color-tertiary-foreground': '28 25 23',
    '--color-muted': '41 37 36',
    '--color-muted-foreground': '168 162 158',
    '--color-accent': '41 37 36',
    '--color-accent-foreground': '250 250 249',
    '--color-destructive': '220 38 38',
    '--color-destructive-foreground': '250 250 249',
    '--color-border': '41 37 36',
    '--color-input': '41 37 36',
    '--color-ring': '248 113 113',
    '--radius': '1rem',
  },
};

// Utility functions
export const getColorValue = (colorPath: string, opacity?: number) => {
  const [colorName, shade] = colorPath.split('-');
  const color = themeConfig.colors[colorName as keyof typeof themeConfig.colors];
  if (color && typeof color === 'object' && shade in color) {
    const value = color[shade as keyof typeof color];
    return opacity ? `${value}${Math.round(opacity * 255).toString(16)}` : value;
  }
  return colorPath;
};

export const applyTheme = (theme: 'light' | 'dark') => {
  const root = document.documentElement;
  const variables = cssVariables[theme];
  
  Object.entries(variables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
};
