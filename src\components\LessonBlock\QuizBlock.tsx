// Quiz Block Component for Lesson Content
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON><PERSON>le, Button } from '@/components/ui';
import { CheckCircle, XCircle, AlertCircle, Trophy } from 'lucide-react';
import type { QuizContent, QuizQuestion } from '@/types';

interface QuizBlockProps {
  content: QuizContent;
  onComplete?: (score: number, totalPoints: number) => void;
  className?: string;
}

export const QuizBlock: React.FC<QuizBlockProps> = ({ 
  content, 
  onComplete,
  className = '' 
}) => {
  const { questions, passingScore = 80 } = content;
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const [quizCompleted, setQuizCompleted] = useState(false);

  const currentQuestion = questions[currentQuestionIndex];
  const totalPoints = questions.reduce((sum, q) => sum + q.points, 0);

  const handleAnswerSelect = (questionId: string, answer: string) => {
    setSelectedAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNext = () => {
    if (currentQuestionIndex < questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    } else {
      handleSubmitQuiz();
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleSubmitQuiz = () => {
    setShowResults(true);
    setQuizCompleted(true);
    
    const score = calculateScore();
    if (onComplete) {
      onComplete(score, totalPoints);
    }
  };

  const calculateScore = () => {
    return questions.reduce((score, question) => {
      const userAnswer = selectedAnswers[question.id];
      const isCorrect = Array.isArray(question.correctAnswer) 
        ? question.correctAnswer.includes(userAnswer)
        : question.correctAnswer === userAnswer;
      
      return score + (isCorrect ? question.points : 0);
    }, 0);
  };

  const getScorePercentage = () => {
    return Math.round((calculateScore() / totalPoints) * 100);
  };

  const isAnswerCorrect = (questionId: string) => {
    const question = questions.find(q => q.id === questionId);
    const userAnswer = selectedAnswers[questionId];
    
    if (!question || !userAnswer) return false;
    
    return Array.isArray(question.correctAnswer) 
      ? question.correctAnswer.includes(userAnswer)
      : question.correctAnswer === userAnswer;
  };

  if (showResults) {
    const score = calculateScore();
    const percentage = getScorePercentage();
    const passed = percentage >= passingScore;

    return (
      <Card className={`mb-4 ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            {passed ? (
              <Trophy className="h-6 w-6 text-yellow-500" />
            ) : (
              <AlertCircle className="h-6 w-6 text-orange-500" />
            )}
            <span>Quiz Results</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Score Summary */}
          <div className="text-center space-y-4">
            <div className={`text-6xl font-bold ${passed ? 'text-green-600' : 'text-orange-600'}`}>
              {percentage}%
            </div>
            <div className="text-lg text-gray-600">
              {score} out of {totalPoints} points
            </div>
            <div className={`text-lg font-medium ${passed ? 'text-green-600' : 'text-orange-600'}`}>
              {passed ? 'Congratulations! You passed!' : 'Keep studying and try again!'}
            </div>
          </div>

          {/* Question Review */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Review:</h3>
            {questions.map((question, index) => {
              const isCorrect = isAnswerCorrect(question.id);
              const userAnswer = selectedAnswers[question.id];
              
              return (
                <div key={question.id} className="border rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    {isCorrect ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mt-1 flex-shrink-0" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 mt-1 flex-shrink-0" />
                    )}
                    <div className="flex-1 space-y-2">
                      <div className="font-medium">
                        {index + 1}. {question.question}
                      </div>
                      <div className="text-sm text-gray-600">
                        Your answer: <span className={isCorrect ? 'text-green-600' : 'text-red-600'}>
                          {userAnswer}
                        </span>
                      </div>
                      {!isCorrect && (
                        <div className="text-sm text-green-600">
                          Correct answer: {Array.isArray(question.correctAnswer) 
                            ? question.correctAnswer.join(', ') 
                            : question.correctAnswer}
                        </div>
                      )}
                      {question.explanation && (
                        <div className="text-sm text-blue-600 bg-blue-50 p-2 rounded">
                          {question.explanation}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="text-center">
            <Button onClick={() => {
              setShowResults(false);
              setQuizCompleted(false);
              setCurrentQuestionIndex(0);
              setSelectedAnswers({});
            }}>
              Retake Quiz
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`mb-4 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Quiz Question {currentQuestionIndex + 1} of {questions.length}</span>
          <span className="text-sm font-normal text-gray-600">
            {currentQuestion.points} point{currentQuestion.points !== 1 ? 's' : ''}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
          ></div>
        </div>

        {/* Question */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{currentQuestion.question}</h3>
          
          {/* Answer Options */}
          {currentQuestion.type === 'multiple-choice' && currentQuestion.options && (
            <div className="space-y-2">
              {currentQuestion.options.map((option, index) => (
                <label 
                  key={index}
                  className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option}
                    checked={selectedAnswers[currentQuestion.id] === option}
                    onChange={() => handleAnswerSelect(currentQuestion.id, option)}
                    className="text-blue-600"
                  />
                  <span className="flex-1">{option}</span>
                </label>
              ))}
            </div>
          )}

          {/* True/False */}
          {currentQuestion.type === 'true-false' && (
            <div className="space-y-2">
              {['True', 'False'].map((option) => (
                <label 
                  key={option}
                  className="flex items-center space-x-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                >
                  <input
                    type="radio"
                    name={`question-${currentQuestion.id}`}
                    value={option}
                    checked={selectedAnswers[currentQuestion.id] === option}
                    onChange={() => handleAnswerSelect(currentQuestion.id, option)}
                    className="text-blue-600"
                  />
                  <span className="flex-1">{option}</span>
                </label>
              ))}
            </div>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
          >
            Previous
          </Button>
          
          <Button
            onClick={handleNext}
            disabled={!selectedAnswers[currentQuestion.id]}
          >
            {currentQuestionIndex === questions.length - 1 ? 'Submit Quiz' : 'Next'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
