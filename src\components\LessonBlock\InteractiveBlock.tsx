// Interactive Block Component for Lesson Content
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, Button } from '@/components/ui';
import { Shuffle, RotateCcw, CheckCircle, XCircle } from 'lucide-react';
import type { InteractiveContent } from '@/types';

interface InteractiveBlockProps {
  content: InteractiveContent;
  className?: string;
}

// Flashcard Component
const FlashcardInteractive: React.FC<{ data: any }> = ({ data }) => {
  const [isFlipped, setIsFlipped] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const { cards } = data;

  const handleNext = () => {
    setCurrentIndex((prev) => (prev + 1) % cards.length);
    setIsFlipped(false);
  };

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + cards.length) % cards.length);
    setIsFlipped(false);
  };

  const currentCard = cards[currentIndex];

  return (
    <div className="space-y-4">
      {/* Flashcard */}
      <div 
        className="relative h-64 cursor-pointer perspective-1000"
        onClick={() => setIsFlipped(!isFlipped)}
      >
        <div className={`relative w-full h-full transition-transform duration-600 transform-style-preserve-3d ${
          isFlipped ? 'rotate-y-180' : ''
        }`}>
          {/* Front */}
          <div className="absolute inset-0 w-full h-full backface-hidden bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-3xl font-japanese mb-2">{currentCard.front}</div>
              <div className="text-sm opacity-75">Click to reveal</div>
            </div>
          </div>
          
          {/* Back */}
          <div className="absolute inset-0 w-full h-full backface-hidden rotate-y-180 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center text-white">
            <div className="text-center">
              <div className="text-2xl mb-2">{currentCard.back}</div>
              {currentCard.example && (
                <div className="text-sm opacity-75 italic">{currentCard.example}</div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <Button variant="outline" onClick={handlePrevious}>
          Previous
        </Button>
        
        <div className="text-sm text-gray-600">
          {currentIndex + 1} of {cards.length}
        </div>
        
        <Button variant="outline" onClick={handleNext}>
          Next
        </Button>
      </div>
    </div>
  );
};

// Fill in the Blank Component
const FillBlankInteractive: React.FC<{ data: any }> = ({ data }) => {
  const [answers, setAnswers] = useState<Record<string, string>>({});
  const [showResults, setShowResults] = useState(false);
  const { sentence, blanks } = data;

  const handleAnswerChange = (blankId: string, value: string) => {
    setAnswers(prev => ({ ...prev, [blankId]: value }));
  };

  const checkAnswers = () => {
    setShowResults(true);
  };

  const resetAnswers = () => {
    setAnswers({});
    setShowResults(false);
  };

  const renderSentence = () => {
    let parts = sentence.split(/(\[BLANK_\d+\])/);
    
    return parts.map((part: string, index: number) => {
      const blankMatch = part.match(/\[BLANK_(\d+)\]/);
      if (blankMatch) {
        const blankId = blankMatch[1];
        const blank = blanks.find((b: any) => b.id === blankId);
        const userAnswer = answers[blankId] || '';
        const isCorrect = showResults && userAnswer.toLowerCase() === blank.answer.toLowerCase();
        const isIncorrect = showResults && userAnswer && !isCorrect;
        
        return (
          <span key={index} className="inline-block mx-1">
            <input
              type="text"
              value={userAnswer}
              onChange={(e) => handleAnswerChange(blankId, e.target.value)}
              className={`border-b-2 bg-transparent text-center min-w-[100px] focus:outline-none ${
                isCorrect ? 'border-green-500 text-green-600' :
                isIncorrect ? 'border-red-500 text-red-600' :
                'border-gray-300 focus:border-blue-500'
              }`}
              placeholder="___"
              disabled={showResults}
            />
            {showResults && isIncorrect && (
              <div className="text-xs text-green-600 mt-1">
                Correct: {blank.answer}
              </div>
            )}
          </span>
        );
      }
      return <span key={index}>{part}</span>;
    });
  };

  return (
    <div className="space-y-6">
      <div className="text-lg leading-relaxed">
        {renderSentence()}
      </div>
      
      <div className="flex space-x-2">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={Object.keys(answers).length === 0}>
            Check Answers
          </Button>
        ) : (
          <Button onClick={resetAnswers}>
            Try Again
          </Button>
        )}
      </div>
    </div>
  );
};

// Matching Component
const MatchingInteractive: React.FC<{ data: any }> = ({ data }) => {
  const [matches, setMatches] = useState<Record<string, string>>({});
  const [selectedLeft, setSelectedLeft] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const { leftItems, rightItems } = data;

  const handleLeftClick = (id: string) => {
    setSelectedLeft(id);
  };

  const handleRightClick = (id: string) => {
    if (selectedLeft) {
      setMatches(prev => ({ ...prev, [selectedLeft]: id }));
      setSelectedLeft(null);
    }
  };

  const checkAnswers = () => {
    setShowResults(true);
  };

  const resetMatches = () => {
    setMatches({});
    setSelectedLeft(null);
    setShowResults(false);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-8">
        {/* Left Column */}
        <div className="space-y-2">
          <h4 className="font-medium text-gray-700">Match these:</h4>
          {leftItems.map((item: any) => {
            const isMatched = Object.keys(matches).includes(item.id);
            const isCorrect = showResults && matches[item.id] === item.correctMatch;
            
            return (
              <div
                key={item.id}
                onClick={() => !showResults && handleLeftClick(item.id)}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedLeft === item.id ? 'border-blue-500 bg-blue-50' :
                  isCorrect ? 'border-green-500 bg-green-50' :
                  showResults && isMatched ? 'border-red-500 bg-red-50' :
                  'border-gray-200 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span>{item.text}</span>
                  {showResults && (
                    isCorrect ? <CheckCircle className="h-4 w-4 text-green-500" /> :
                    isMatched ? <XCircle className="h-4 w-4 text-red-500" /> : null
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Right Column */}
        <div className="space-y-2">
          <h4 className="font-medium text-gray-700">With these:</h4>
          {rightItems.map((item: any) => {
            const isMatched = Object.values(matches).includes(item.id);
            
            return (
              <div
                key={item.id}
                onClick={() => !showResults && handleRightClick(item.id)}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  isMatched ? 'border-gray-400 bg-gray-100' :
                  'border-gray-200 hover:border-gray-300'
                }`}
              >
                {item.text}
              </div>
            );
          })}
        </div>
      </div>

      <div className="flex space-x-2">
        {!showResults ? (
          <Button onClick={checkAnswers} disabled={Object.keys(matches).length === 0}>
            Check Matches
          </Button>
        ) : (
          <Button onClick={resetMatches}>
            Try Again
          </Button>
        )}
      </div>
    </div>
  );
};

export const InteractiveBlock: React.FC<InteractiveBlockProps> = ({ content, className = '' }) => {
  const { type, data } = content;

  const renderInteractive = () => {
    switch (type) {
      case 'flashcard':
        return <FlashcardInteractive data={data} />;
      case 'fill-blank':
        return <FillBlankInteractive data={data} />;
      case 'matching':
        return <MatchingInteractive data={data} />;
      case 'drag-drop':
        // Placeholder for drag-drop implementation
        return (
          <div className="text-center py-8 text-gray-500">
            Drag-and-drop interactive coming soon!
          </div>
        );
      default:
        return (
          <div className="text-center py-8 text-gray-500">
            Unknown interactive type: {type}
          </div>
        );
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'flashcard':
        return 'Flashcards';
      case 'fill-blank':
        return 'Fill in the Blanks';
      case 'matching':
        return 'Matching Exercise';
      case 'drag-drop':
        return 'Drag and Drop';
      default:
        return 'Interactive Exercise';
    }
  };

  return (
    <Card className={`mb-4 ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Shuffle className="h-5 w-5 text-blue-600" />
          <span>{getTitle()}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {renderInteractive()}
      </CardContent>
    </Card>
  );
};
