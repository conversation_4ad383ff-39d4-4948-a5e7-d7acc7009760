import { useMemo } from 'react';
import { useAppStore } from '@/stores/useAppStore';
import type { Lesson, UserProgress } from '@/types';

export type LessonStatus = 'new' | 'in_progress' | 'completed' | 'suggested';

export interface LessonWithTag extends Lesson {
  status: LessonStatus;
  isRecommended?: boolean;
  difficultyMatch?: boolean;
  nextInSequence?: boolean;
}

export interface LessonTagConfig {
  status: LessonStatus;
  label: string;
  color: string;
  bgColor: string;
  icon: string;
  priority: number;
}

export const LESSON_TAG_CONFIGS: Record<LessonStatus, LessonTagConfig> = {
  new: {
    status: 'new',
    label: 'New',
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-100 dark:bg-blue-900/30',
    icon: '✨',
    priority: 3,
  },
  in_progress: {
    status: 'in_progress',
    label: 'In Progress',
    color: 'text-orange-600 dark:text-orange-400',
    bgColor: 'bg-orange-100 dark:bg-orange-900/30',
    icon: '⏳',
    priority: 1,
  },
  completed: {
    status: 'completed',
    label: 'Completed',
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-100 dark:bg-green-900/30',
    icon: '✅',
    priority: 4,
  },
  suggested: {
    status: 'suggested',
    label: 'Suggested',
    color: 'text-purple-600 dark:text-purple-400',
    bgColor: 'bg-purple-100 dark:bg-purple-900/30',
    icon: '💡',
    priority: 2,
  },
};

export function useLessonTags(lessons: Lesson[]) {
  const { user, userProgress } = useAppStore();

  const lessonsWithTags = useMemo(() => {
    if (!lessons || !user) return [];

    return lessons.map((lesson): LessonWithTag => {
      const progress = userProgress.find(p => p.lessonId === lesson.id);
      
      // Determine status based on progress
      let status: LessonStatus = 'new';
      
      if (progress) {
        switch (progress.status) {
          case 'completed':
          case 'mastered':
            status = 'completed';
            break;
          case 'in-progress':
            status = 'in_progress';
            break;
          default:
            status = 'new';
        }
      } else {
        // Check if this lesson should be suggested
        const userLevel = user.currentLevel;
        const isLevelMatch = lesson.level === userLevel;
        const hasPrerequisites = lesson.prerequisites && lesson.prerequisites.length > 0;
        
        if (hasPrerequisites) {
          const prerequisitesMet = lesson.prerequisites!.every(prereqId => {
            const prereqProgress = userProgress.find(p => p.lessonId === prereqId);
            return prereqProgress && (prereqProgress.status === 'completed' || prereqProgress.status === 'mastered');
          });
          
          if (prerequisitesMet && isLevelMatch) {
            status = 'suggested';
          }
        } else if (isLevelMatch) {
          status = 'suggested';
        }
      }

      // Additional metadata for enhanced recommendations
      const isRecommended = status === 'suggested' || (
        status === 'new' && 
        lesson.level === user.currentLevel &&
        lesson.difficulty <= 3
      );

      const difficultyMatch = Math.abs(lesson.difficulty - 2) <= 1; // Prefer medium difficulty
      
      const nextInSequence = lesson.prerequisites?.some(prereqId => {
        const prereqProgress = userProgress.find(p => p.lessonId === prereqId);
        return prereqProgress && prereqProgress.status === 'completed';
      }) || false;

      return {
        ...lesson,
        status,
        isRecommended,
        difficultyMatch,
        nextInSequence,
      };
    });
  }, [lessons, user, userProgress]);

  const filterByStatus = (status: LessonStatus | LessonStatus[]) => {
    const statusArray = Array.isArray(status) ? status : [status];
    return lessonsWithTags.filter(lesson => statusArray.includes(lesson.status));
  };

  const getRecommendedLessons = (limit = 3) => {
    return lessonsWithTags
      .filter(lesson => lesson.isRecommended)
      .sort((a, b) => {
        // Sort by priority: in_progress > suggested > new > completed
        const priorityA = LESSON_TAG_CONFIGS[a.status].priority;
        const priorityB = LESSON_TAG_CONFIGS[b.status].priority;
        
        if (priorityA !== priorityB) {
          return priorityA - priorityB;
        }
        
        // Secondary sort by difficulty match and sequence
        const scoreA = (a.difficultyMatch ? 2 : 0) + (a.nextInSequence ? 1 : 0);
        const scoreB = (b.difficultyMatch ? 2 : 0) + (b.nextInSequence ? 1 : 0);
        
        return scoreB - scoreA;
      })
      .slice(0, limit);
  };

  const getStatusCounts = () => {
    const counts = {
      new: 0,
      in_progress: 0,
      completed: 0,
      suggested: 0,
    };

    lessonsWithTags.forEach(lesson => {
      counts[lesson.status]++;
    });

    return counts;
  };

  return {
    lessonsWithTags,
    filterByStatus,
    getRecommendedLessons,
    getStatusCounts,
    tagConfigs: LESSON_TAG_CONFIGS,
  };
}

export default useLessonTags;
