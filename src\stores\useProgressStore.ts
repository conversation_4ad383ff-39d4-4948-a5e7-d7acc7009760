// Progress and Completion Tracking Store
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface LessonProgress {
  lessonId: string;
  completed: boolean;
  completedAt?: Date;
  timeSpent: number; // in minutes
  quizScores: Record<string, { score: number; totalPoints: number; attempts: number }>;
  feedback?: {
    helpful: boolean;
    rating: 1 | 2 | 3 | 4 | 5; // emoji-based rating
    comment?: string;
    submittedAt: Date;
  };
}

export interface StudySession {
  id: string;
  lessonId: string;
  startTime: Date;
  endTime?: Date;
  timeSpent: number;
  completed: boolean;
}

export interface ProgressStats {
  totalLessonsCompleted: number;
  totalTimeSpent: number; // in minutes
  currentStreak: number; // consecutive days
  longestStreak: number;
  lastStudyDate?: Date;
  averageSessionTime: number;
  completionRate: number; // percentage
}

interface ProgressStore {
  // Progress tracking
  lessonProgress: Record<string, LessonProgress>;
  studySessions: StudySession[];
  stats: ProgressStats;
  
  // Current session
  currentSession: StudySession | null;
  
  // Actions
  startLessonSession: (lessonId: string) => void;
  endLessonSession: (completed?: boolean) => void;
  markLessonCompleted: (lessonId: string, timeSpent?: number) => void;
  updateQuizScore: (lessonId: string, quizId: string, score: number, totalPoints: number) => void;
  submitLessonFeedback: (lessonId: string, feedback: { helpful: boolean; rating: 1 | 2 | 3 | 4 | 5; comment?: string }) => void;
  
  // Getters
  getLessonProgress: (lessonId: string) => LessonProgress | null;
  isLessonCompleted: (lessonId: string) => boolean;
  getCompletionPercentage: (lessonIds: string[]) => number;
  getStudyStreak: () => number;
  getTotalStudyTime: () => number;
  
  // Utilities
  resetProgress: () => void;
  exportProgress: () => string;
  importProgress: (data: string) => boolean;
}

const initialStats: ProgressStats = {
  totalLessonsCompleted: 0,
  totalTimeSpent: 0,
  currentStreak: 0,
  longestStreak: 0,
  averageSessionTime: 0,
  completionRate: 0,
};

export const useProgressStore = create<ProgressStore>()(
  persist(
    (set, get) => ({
      lessonProgress: {},
      studySessions: [],
      stats: initialStats,
      currentSession: null,

      startLessonSession: (lessonId: string) => {
        const session: StudySession = {
          id: `session-${Date.now()}`,
          lessonId,
          startTime: new Date(),
          timeSpent: 0,
          completed: false,
        };

        set({ currentSession: session });
      },

      endLessonSession: (completed = false) => {
        const { currentSession, studySessions } = get();
        if (!currentSession) return;

        const endTime = new Date();
        const timeSpent = Math.round((endTime.getTime() - currentSession.startTime.getTime()) / (1000 * 60));
        
        const completedSession: StudySession = {
          ...currentSession,
          endTime,
          timeSpent,
          completed,
        };

        set((state) => ({
          currentSession: null,
          studySessions: [...state.studySessions, completedSession],
          stats: {
            ...state.stats,
            totalTimeSpent: state.stats.totalTimeSpent + timeSpent,
            averageSessionTime: calculateAverageSessionTime([...state.studySessions, completedSession]),
          },
        }));

        if (completed) {
          get().markLessonCompleted(currentSession.lessonId, timeSpent);
        }
      },

      markLessonCompleted: (lessonId: string, timeSpent = 0) => {
        set((state) => {
          const existingProgress = state.lessonProgress[lessonId];
          const now = new Date();
          
          const updatedProgress: LessonProgress = {
            ...existingProgress,
            lessonId,
            completed: true,
            completedAt: now,
            timeSpent: (existingProgress?.timeSpent || 0) + timeSpent,
            quizScores: existingProgress?.quizScores || {},
          };

          const newLessonProgress = {
            ...state.lessonProgress,
            [lessonId]: updatedProgress,
          };

          const completedLessons = Object.values(newLessonProgress).filter(p => p.completed);
          const streak = calculateStudyStreak(completedLessons);

          return {
            lessonProgress: newLessonProgress,
            stats: {
              ...state.stats,
              totalLessonsCompleted: completedLessons.length,
              currentStreak: streak.current,
              longestStreak: Math.max(streak.longest, state.stats.longestStreak),
              lastStudyDate: now,
              completionRate: calculateCompletionRate(newLessonProgress),
            },
          };
        });
      },

      updateQuizScore: (lessonId: string, quizId: string, score: number, totalPoints: number) => {
        set((state) => {
          const existingProgress = state.lessonProgress[lessonId] || {
            lessonId,
            completed: false,
            timeSpent: 0,
            quizScores: {},
          };

          const existingQuizScore = existingProgress.quizScores[quizId];
          const attempts = (existingQuizScore?.attempts || 0) + 1;

          return {
            lessonProgress: {
              ...state.lessonProgress,
              [lessonId]: {
                ...existingProgress,
                quizScores: {
                  ...existingProgress.quizScores,
                  [quizId]: {
                    score: Math.max(score, existingQuizScore?.score || 0), // Keep best score
                    totalPoints,
                    attempts,
                  },
                },
              },
            },
          };
        });
      },

      submitLessonFeedback: (lessonId: string, feedback) => {
        set((state) => {
          const existingProgress = state.lessonProgress[lessonId] || {
            lessonId,
            completed: false,
            timeSpent: 0,
            quizScores: {},
          };

          return {
            lessonProgress: {
              ...state.lessonProgress,
              [lessonId]: {
                ...existingProgress,
                feedback: {
                  ...feedback,
                  submittedAt: new Date(),
                },
              },
            },
          };
        });
      },

      getLessonProgress: (lessonId: string) => {
        return get().lessonProgress[lessonId] || null;
      },

      isLessonCompleted: (lessonId: string) => {
        return get().lessonProgress[lessonId]?.completed || false;
      },

      getCompletionPercentage: (lessonIds: string[]) => {
        const { lessonProgress } = get();
        const completed = lessonIds.filter(id => lessonProgress[id]?.completed).length;
        return lessonIds.length > 0 ? Math.round((completed / lessonIds.length) * 100) : 0;
      },

      getStudyStreak: () => {
        return get().stats.currentStreak;
      },

      getTotalStudyTime: () => {
        return get().stats.totalTimeSpent;
      },

      resetProgress: () => {
        set({
          lessonProgress: {},
          studySessions: [],
          stats: initialStats,
          currentSession: null,
        });
      },

      exportProgress: () => {
        const { lessonProgress, studySessions, stats } = get();
        return JSON.stringify({
          lessonProgress,
          studySessions,
          stats,
          exportedAt: new Date().toISOString(),
        }, null, 2);
      },

      importProgress: (data: string) => {
        try {
          const parsed = JSON.parse(data);
          if (parsed.lessonProgress && parsed.studySessions && parsed.stats) {
            set({
              lessonProgress: parsed.lessonProgress,
              studySessions: parsed.studySessions,
              stats: parsed.stats,
            });
            return true;
          }
          return false;
        } catch {
          return false;
        }
      },
    }),
    {
      name: 'japanese-learning-progress',
      version: 1,
    }
  )
);

// Helper functions
const calculateAverageSessionTime = (sessions: StudySession[]): number => {
  if (sessions.length === 0) return 0;
  const totalTime = sessions.reduce((sum, session) => sum + session.timeSpent, 0);
  return Math.round(totalTime / sessions.length);
};

const calculateStudyStreak = (completedLessons: LessonProgress[]): { current: number; longest: number } => {
  if (completedLessons.length === 0) return { current: 0, longest: 0 };

  // Sort by completion date
  const sorted = completedLessons
    .filter(lesson => lesson.completedAt)
    .sort((a, b) => new Date(b.completedAt!).getTime() - new Date(a.completedAt!).getTime());

  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 0;
  let lastDate: Date | null = null;

  for (const lesson of sorted) {
    const completedDate = new Date(lesson.completedAt!);
    const daysDiff = lastDate ? Math.floor((lastDate.getTime() - completedDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;

    if (lastDate === null || daysDiff <= 1) {
      tempStreak++;
      if (lastDate === null) currentStreak = tempStreak;
    } else {
      longestStreak = Math.max(longestStreak, tempStreak);
      tempStreak = 1;
    }

    lastDate = completedDate;
  }

  longestStreak = Math.max(longestStreak, tempStreak);
  
  // Check if current streak is still active (studied within last 2 days)
  const now = new Date();
  const daysSinceLastStudy = lastDate ? Math.floor((now.getTime() - lastDate.getTime()) / (1000 * 60 * 60 * 24)) : Infinity;
  
  if (daysSinceLastStudy > 1) {
    currentStreak = 0;
  }

  return { current: currentStreak, longest: longestStreak };
};

const calculateCompletionRate = (lessonProgress: Record<string, LessonProgress>): number => {
  const lessons = Object.values(lessonProgress);
  if (lessons.length === 0) return 0;
  
  const completed = lessons.filter(lesson => lesson.completed).length;
  return Math.round((completed / lessons.length) * 100);
};
