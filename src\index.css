@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+JP:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Dynamic Theming */
:root {
  --color-background: 255 255 255;
  --color-foreground: 28 25 23;
  --color-card: 255 255 255;
  --color-card-foreground: 28 25 23;
  --color-primary: 239 68 68;
  --color-primary-foreground: 255 255 255;
  --color-secondary: 14 165 233;
  --color-secondary-foreground: 255 255 255;
  --color-tertiary: 34 197 94;
  --color-tertiary-foreground: 255 255 255;
  --radius: 1rem;
}

.dark {
  --color-background: 12 10 9;
  --color-foreground: 250 250 249;
  --color-card: 28 25 23;
  --color-card-foreground: 250 250 249;
  --color-primary: 248 113 113;
  --color-primary-foreground: 28 25 23;
  --color-secondary: 56 189 248;
  --color-secondary-foreground: 28 25 23;
  --color-tertiary: 74 222 128;
  --color-tertiary-foreground: 28 25 23;
}

/* Enhanced Glassmorphism Effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(41, 37, 36, 0.8);
  border: 1px solid rgba(120, 113, 108, 0.3);
}

/* Smooth Transitions for All Interactive Elements */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(245 245 244);
}

.dark ::-webkit-scrollbar-track {
  background: rgb(41 37 36);
}

::-webkit-scrollbar-thumb {
  background: rgb(168 162 158);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(120 113 108);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(87 83 78);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgb(120 113 108);
}

/* Focus Styles for Accessibility */
.focus-visible:focus-visible {
  outline: 2px solid rgb(239 68 68);
  outline-offset: 2px;
}

/* Animation Performance Optimization */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: "rlig" 1, "calt" 1;
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Dark mode base styles */
  .dark {
    color-scheme: dark;
  }

  .dark body {
    background-color: #0f172a;
    color: #f1f5f9;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none px-4 py-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }

  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white hover:bg-gray-50 active:bg-gray-100;
  }

  .card {
    @apply rounded-lg border border-gray-200 bg-white shadow-sm p-6;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .animate-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .slide-in-from-bottom {
    animation: slideUp 0.3s ease-out;
  }

  .slide-in-from-top {
    animation: slideDown 0.3s ease-out;
  }

  .scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  /* Japanese font support */
  .font-japanese {
    font-family: 'Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', 'Meiryo', sans-serif;
  }

  /* Animation classes */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  /* 3D Transform utilities for flashcards */
  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-preserve-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  /* Enhanced hover and focus effects */
  .hover-lift {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .focus-ring {
    transition: box-shadow 0.2s ease-in-out;
  }

  .focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
  }

  /* Smooth transitions for interactive elements */
  .transition-all-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Gradient text effects */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Pulse animation for loading states */
  .pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
