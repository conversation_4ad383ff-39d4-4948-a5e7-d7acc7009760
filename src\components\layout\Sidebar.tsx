import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Home,
  BookOpen,
  PenTool,
  Type,
  Headphones,
  BarChart3,
  Settings,
  X,
  ChevronRight,
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { Progress } from '@/components/ui/Progress';
import { useAppStore } from '@/stores/useAppStore';
import { JLPT_LEVEL_INFO, CATEGORY_INFO } from '@/utils';
import { cn } from '@/utils/cn';
import type { JLPTLevel, LessonCategory } from '@/types';

interface SidebarProps {
  className?: string;
}

const navigationItems = [
  { icon: Home, label: 'Dashboard', path: '/' },
  { icon: BookOpen, label: 'Lessons', path: '/lessons' },
  { icon: BarChart3, label: 'Progress', path: '/progress' },
  { icon: Settings, label: 'Settings', path: '/settings' },
];

const categoryIcons = {
  grammar: PenTool,
  vocabulary: BookOpen,
  kanji: Type,
  reading: BookOpen,
  listening: Headphones,
};

export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { sidebarOpen, currentLevel } = useAppStore();
  const { setSidebarOpen, setCurrentLevel } = useAppStore();

  const levels: JLPTLevel[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
  const categories: LessonCategory[] = [
    'grammar',
    'vocabulary',
    'kanji',
    'reading',
    'listening',
  ];

  // Mock progress data - in real app this would come from the store
  const levelProgress = {
    N5: 85,
    N4: 60,
    N3: 30,
    N2: 10,
    N1: 0,
  };

  return (
    <>
      {/* Mobile overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          'fixed left-0 top-0 z-50 h-full w-64 transform border-r border-gray-200 bg-white transition-transform duration-200 ease-in-out md:relative md:translate-x-0',
          sidebarOpen ? 'translate-x-0' : '-translate-x-full',
          className
        )}
      >
        <div className="flex h-full flex-col">
          {/* Header */}
          <div className="flex h-16 items-center justify-between border-b border-gray-200 px-4">
            <div className="flex items-center space-x-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary-600 text-white font-bold text-sm">
                日
              </div>
              <span className="font-semibold text-gray-900">Menu</span>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setSidebarOpen(false)}
              className="md:hidden"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 space-y-1 p-4">
            {/* Main Navigation */}
            <div className="space-y-1">
              {navigationItems.map(item => {
                const Icon = item.icon;
                const isActive = location.pathname === item.path;
                return (
                  <Button
                    key={item.path}
                    variant={isActive ? 'secondary' : 'ghost'}
                    className="w-full justify-start"
                    onClick={() => navigate(item.path)}
                  >
                    <Icon className="mr-3 h-4 w-4" />
                    {item.label}
                  </Button>
                );
              })}
            </div>

            {/* JLPT Levels */}
            <div className="pt-6">
              <h3 className="mb-3 px-2 text-xs font-semibold uppercase tracking-wider text-gray-500">
                JLPT Levels
              </h3>
              <div className="space-y-2">
                {levels.map(level => {
                  const levelInfo = JLPT_LEVEL_INFO[level];
                  const progress = levelProgress[level];
                  const isActive = level === currentLevel;

                  return (
                    <div key={level} className="space-y-2">
                      <Button
                        variant={isActive ? 'secondary' : 'ghost'}
                        className="w-full justify-between"
                        onClick={() => {
                          setCurrentLevel(level);
                          navigate(`/level/${level.toLowerCase()}`);
                        }}
                      >
                        <div className="flex items-center">
                          <div
                            className={cn(
                              'mr-3 h-3 w-3 rounded-full',
                              levelInfo.color
                            )}
                          />
                          <span className="font-medium">{level}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" size="sm">
                            {progress}%
                          </Badge>
                          <ChevronRight className="h-4 w-4" />
                        </div>
                      </Button>

                      {isActive && (
                        <div className="ml-6 space-y-1">
                          <Progress
                            value={progress}
                            size="sm"
                            className="mb-2"
                          />
                          {categories.map(category => {
                            const Icon = categoryIcons[category];
                            const categoryInfo = CATEGORY_INFO[category];

                            return (
                              <Button
                                key={category}
                                variant="ghost"
                                size="sm"
                                className="w-full justify-start pl-4"
                                onClick={() =>
                                  navigate(
                                    `/level/${level.toLowerCase()}/category/${category}`
                                  )
                                }
                              >
                                <Icon className="mr-2 h-3 w-3" />
                                <span className="text-xs">
                                  {categoryInfo.name}
                                </span>
                              </Button>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </nav>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <div className="rounded-lg bg-primary-50 p-3">
              <h4 className="text-sm font-medium text-primary-900">
                Daily Goal
              </h4>
              <p className="text-xs text-primary-700 mb-2">
                15 minutes remaining
              </p>
              <Progress value={75} size="sm" variant="default" />
            </div>
          </div>
        </div>
      </aside>
    </>
  );
};
