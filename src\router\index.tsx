import React from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { Layout } from '@/components/layout';
import { Dashboard } from '@/pages/Dashboard';
import { JLPTLevel } from '@/pages/JLPTLevel';
import { CategoryLessons } from '@/pages/CategoryLessons';
import { LessonViewer } from '@/pages/LessonViewer';
import { ContentManager } from '@/pages/ContentManager';

// Layout wrapper component
const LayoutWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return <Layout>{children}</Layout>;
};

// Error boundary component
const ErrorPage: React.FC = () => {
  return (
    <LayoutWrapper>
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h1>
        <p className="text-gray-600 mb-6">The page you're looking for doesn't exist.</p>
        <a href="/" className="text-primary-600 hover:text-primary-700 font-medium">
          Go back to Dashboard
        </a>
      </div>
    </LayoutWrapper>
  );
};

// Router configuration
export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <LayoutWrapper>
        <Dashboard />
      </LayoutWrapper>
    ),
    errorElement: <ErrorPage />,
  },
  {
    path: '/dashboard',
    element: (
      <LayoutWrapper>
        <Dashboard />
      </LayoutWrapper>
    ),
  },
  {
    path: '/level/:level',
    element: (
      <LayoutWrapper>
        <JLPTLevel />
      </LayoutWrapper>
    ),
  },
  {
    path: '/level/:level/category/:category',
    element: (
      <LayoutWrapper>
        <CategoryLessons />
      </LayoutWrapper>
    ),
  },
  {
    path: '/lesson/:lessonId',
    element: <LessonViewer />, // No layout wrapper for full-screen lesson viewer
  },
  {
    path: '/progress',
    element: (
      <LayoutWrapper>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Progress Page</h1>
          <p className="text-gray-600">Coming soon...</p>
        </div>
      </LayoutWrapper>
    ),
  },
  {
    path: '/content',
    element: <ContentManager />, // No layout wrapper for full-screen content manager
  },
  {
    path: '/settings',
    element: (
      <LayoutWrapper>
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Settings Page</h1>
          <p className="text-gray-600">Coming soon...</p>
        </div>
      </LayoutWrapper>
    ),
  },
]);

// Router provider component
export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};
