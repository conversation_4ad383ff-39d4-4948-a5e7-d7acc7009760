import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Badge,
  Progress,
  Input,
} from '@/components/ui';
import { 
  ArrowLeft, 
  Search, 
  Filter, 
  Clock, 
  Play, 
  CheckCircle, 
  Circle,
  Star,
  BookOpen
} from 'lucide-react';
import { JLPT_LEVEL_INFO, CATEGORY_INFO } from '@/utils';
import { contentLoader, type CategoryContent } from '@/content';
import type { JLPTLevel, LessonCategory, Lesson } from '@/types';

export const CategoryLessons: React.FC = () => {
  const navigate = useNavigate();
  const { level, category } = useParams<{ level: string; category: string }>();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterDifficulty, setFilterDifficulty] = useState<string>('all');
  const [categoryContent, setCategoryContent] = useState<CategoryContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const jlptLevel = level?.toUpperCase() as JLPTLevel;
  const lessonCategory = category as LessonCategory;

  const levelInfo = JLPT_LEVEL_INFO[jlptLevel];
  const categoryInfo = CATEGORY_INFO[lessonCategory];

  // Load dynamic content
  useEffect(() => {
    const loadContent = async () => {
      if (!jlptLevel || !lessonCategory) return;

      setLoading(true);
      setError(null);

      try {
        const content = await contentLoader.loadCategoryContent(jlptLevel, lessonCategory);
        setCategoryContent(content);
      } catch (err) {
        setError('Failed to load category content');
        console.error('Error loading category content:', err);
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, [jlptLevel, lessonCategory]);

  if (!levelInfo || !categoryInfo) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h1>
        <Button onClick={() => navigate('/')}>Back to Dashboard</Button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading lessons...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Content</h1>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={() => navigate(`/level/${level}`)}>Back to Level</Button>
      </div>
    );
  }

  // Get lessons from dynamic content
  const lessons = categoryContent ? categoryContent.lessons.map(lesson => ({
    id: lesson.id,
    title: lesson.title,
    description: lesson.description,
    difficulty: lesson.difficulty,
    estimatedTime: lesson.estimatedTime,
    completed: Math.random() > 0.5, // Random completion status for demo
    rating: 4.5 + Math.random() * 0.5, // Random rating between 4.5-5.0
    topics: lesson.tags || [],
  })) : [];

  const filteredLessons = lessons.filter(lesson => {
    const matchesSearch = lesson.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lesson.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDifficulty = filterDifficulty === 'all' || 
                             lesson.difficulty.toString() === filterDifficulty;
    return matchesSearch && matchesDifficulty;
  });

  const completedCount = lessons.filter(l => l.completed).length;
  const completionRate = Math.round((completedCount / lessons.length) * 100);

  const getDifficultyLabel = (difficulty: number) => {
    switch (difficulty) {
      case 1: return 'Easy';
      case 2: return 'Medium';
      case 3: return 'Hard';
      default: return 'Unknown';
    }
  };

  const getDifficultyColor = (difficulty: number) => {
    switch (difficulty) {
      case 1: return 'bg-green-100 text-green-800';
      case 2: return 'bg-yellow-100 text-yellow-800';
      case 3: return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Button 
          variant="ghost" 
          onClick={() => navigate(`/level/${level}`)}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to {jlptLevel}
        </Button>
        
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-primary-100 rounded-lg">
                <BookOpen className="h-6 w-6 text-primary-600" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  {categoryInfo.name} - {jlptLevel}
                </h1>
                <p className="text-xl text-gray-600">{categoryInfo.description}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>{lessons.length} lessons</span>
              <span>•</span>
              <span>{completedCount} completed</span>
              <span>•</span>
              <span>{lessons.reduce((acc, l) => acc + l.estimatedTime, 0)} minutes total</span>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Your Progress</CardTitle>
          <CardDescription>
            {completedCount} of {lessons.length} lessons completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Progress value={completionRate} className="h-3" />
          <div className="flex justify-between text-sm mt-2">
            <span className="text-gray-600">{completionRate}% Complete</span>
            <span className="font-medium text-gray-900">
              {lessons.length - completedCount} lessons remaining
            </span>
          </div>
        </CardContent>
      </Card>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search lessons..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-400" />
          <select
            value={filterDifficulty}
            onChange={(e) => setFilterDifficulty(e.target.value)}
            className="rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-primary-500 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          >
            <option value="all">All Difficulties</option>
            <option value="1">Easy</option>
            <option value="2">Medium</option>
            <option value="3">Hard</option>
          </select>
        </div>
      </div>

      {/* Lessons Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredLessons.map((lesson) => (
          <Card 
            key={lesson.id} 
            className="hover:shadow-md transition-shadow cursor-pointer group"
            onClick={() => navigate(`/lesson/${lesson.id}`)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    {lesson.completed ? (
                      <CheckCircle className="h-5 w-5 text-success-600" />
                    ) : (
                      <Circle className="h-5 w-5 text-gray-400" />
                    )}
                    <Badge 
                      variant="outline" 
                      className={getDifficultyColor(lesson.difficulty)}
                    >
                      {getDifficultyLabel(lesson.difficulty)}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg group-hover:text-primary-600 transition-colors">
                    {lesson.title}
                  </CardTitle>
                  <CardDescription className="mt-1">
                    {lesson.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{lesson.estimatedTime} min</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 text-yellow-500" />
                    <span>{lesson.rating}</span>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {lesson.topics.slice(0, 3).map((topic, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {topic}
                    </Badge>
                  ))}
                  {lesson.topics.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{lesson.topics.length - 3} more
                    </Badge>
                  )}
                </div>
                
                <Button 
                  className="w-full" 
                  variant={lesson.completed ? "outline" : "default"}
                  size="sm"
                >
                  <Play className="mr-2 h-4 w-4" />
                  {lesson.completed ? 'Review' : 'Start Lesson'}
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredLessons.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-600">No lessons found matching your criteria.</p>
          <Button 
            variant="outline" 
            onClick={() => {
              setSearchQuery('');
              setFilterDifficulty('all');
            }}
            className="mt-4"
          >
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  );
};
