// Image Block Component for Lesson Content
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui';
import { ZoomIn, ZoomOut } from 'lucide-react';
import type { ImageContent } from '@/types';

interface ImageBlockProps {
  content: ImageContent;
  className?: string;
}

export const ImageBlock: React.FC<ImageBlockProps> = ({ content, className = '' }) => {
  const { url, alt, caption } = content;
  const [isZoomed, setIsZoomed] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageClick = () => {
    setIsZoomed(!isZoomed);
  };

  return (
    <Card className={`mb-4 ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Image Container */}
          <div className="relative group">
            {!imageLoaded && !imageError && (
              <div className="w-full h-64 bg-gray-200 animate-pulse rounded-lg flex items-center justify-center">
                <div className="text-gray-500">Loading image...</div>
              </div>
            )}
            
            {imageError && (
              <div className="w-full h-64 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <div className="text-lg mb-2">Image not available</div>
                  <div className="text-sm">{alt}</div>
                </div>
              </div>
            )}

            {!imageError && (
              <div className="relative">
                <img
                  src={url}
                  alt={alt}
                  className={`w-full h-auto rounded-lg cursor-pointer transition-all duration-300 ${
                    isZoomed ? 'scale-150 z-10' : 'hover:scale-105'
                  } ${imageLoaded ? 'opacity-100' : 'opacity-0'}`}
                  onClick={handleImageClick}
                  onLoad={() => setImageLoaded(true)}
                  onError={() => setImageError(true)}
                />
                
                {/* Zoom Indicator */}
                {imageLoaded && (
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                    <div className="bg-black bg-opacity-50 text-white p-2 rounded-full">
                      {isZoomed ? (
                        <ZoomOut className="h-4 w-4" />
                      ) : (
                        <ZoomIn className="h-4 w-4" />
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Caption */}
          {caption && (
            <div className="text-center">
              <div className="text-sm text-gray-600 italic">
                {caption}
              </div>
            </div>
          )}

          {/* Click to zoom hint */}
          {imageLoaded && !imageError && (
            <div className="text-center">
              <div className="text-xs text-gray-500">
                Click image to {isZoomed ? 'zoom out' : 'zoom in'}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
