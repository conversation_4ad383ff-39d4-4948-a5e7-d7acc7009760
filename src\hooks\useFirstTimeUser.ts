import { useState, useEffect, useCallback } from 'react';
import { storage } from '@/utils';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  target: string; // CSS selector or element ID
  position: 'top' | 'bottom' | 'left' | 'right';
  action?: 'click' | 'hover' | 'none';
  optional?: boolean;
}

export interface OnboardingState {
  isFirstTime: boolean;
  currentStep: number;
  isActive: boolean;
  completedSteps: string[];
  skippedSteps: string[];
}

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to Your Dashboard!',
    description: 'This is your learning hub where you can track progress and continue your Japanese journey.',
    target: '[data-onboarding="welcome-section"]',
    position: 'bottom',
    action: 'none',
  },
  {
    id: 'stats',
    title: 'Track Your Progress',
    description: 'Click on any stat card to see detailed insights about your learning journey.',
    target: '[data-onboarding="stats-section"]',
    position: 'bottom',
    action: 'click',
  },
  {
    id: 'continue-learning',
    title: 'Continue Learning',
    description: 'Your recommended lessons appear here. Click to start where you left off!',
    target: '[data-onboarding="continue-button"]',
    position: 'top',
    action: 'click',
  },
  {
    id: 'lesson-tags',
    title: 'Lesson Status Tags',
    description: 'Each lesson shows its status: New, In Progress, Completed, or Suggested for you.',
    target: '[data-onboarding="lesson-card"]',
    position: 'right',
    action: 'none',
  },
  {
    id: 'jlpt-progress',
    title: 'JLPT Level Tracking',
    description: 'Monitor your progress across all JLPT levels from N5 to N1.',
    target: '[data-onboarding="jlpt-sidebar"]',
    position: 'left',
    action: 'none',
  },
  {
    id: 'categories',
    title: 'Learning Categories',
    description: 'Switch between Continue Learning and Categories to explore different types of lessons.',
    target: '[data-onboarding="tab-navigation"]',
    position: 'bottom',
    action: 'click',
    optional: true,
  },
];

const STORAGE_KEY = 'japanese-learning-onboarding';

export function useFirstTimeUser() {
  const [onboardingState, setOnboardingState] = useState<OnboardingState>(() => {
    const saved = storage.get(STORAGE_KEY, null);
    
    if (saved) {
      return {
        isFirstTime: false,
        currentStep: 0,
        isActive: false,
        completedSteps: saved.completedSteps || [],
        skippedSteps: saved.skippedSteps || [],
      };
    }

    return {
      isFirstTime: true,
      currentStep: 0,
      isActive: false,
      completedSteps: [],
      skippedSteps: [],
    };
  });

  const [isOnboardingComplete, setIsOnboardingComplete] = useState(false);

  // Check if user has completed onboarding
  useEffect(() => {
    const hasCompletedOnboarding = storage.get(`${STORAGE_KEY}-completed`, false);
    setIsOnboardingComplete(hasCompletedOnboarding);
    
    if (!hasCompletedOnboarding && onboardingState.isFirstTime) {
      // Auto-start onboarding for first-time users after a short delay
      const timer = setTimeout(() => {
        startOnboarding();
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, [onboardingState.isFirstTime]);

  const saveOnboardingState = useCallback((state: Partial<OnboardingState>) => {
    const newState = { ...onboardingState, ...state };
    setOnboardingState(newState);
    
    storage.set(STORAGE_KEY, {
      completedSteps: newState.completedSteps,
      skippedSteps: newState.skippedSteps,
      isFirstTime: false,
    });
  }, [onboardingState]);

  const startOnboarding = useCallback(() => {
    setOnboardingState(prev => ({
      ...prev,
      isActive: true,
      currentStep: 0,
    }));
  }, []);

  const nextStep = useCallback(() => {
    const currentStepData = ONBOARDING_STEPS[onboardingState.currentStep];
    
    if (currentStepData) {
      const newCompletedSteps = [...onboardingState.completedSteps, currentStepData.id];
      
      if (onboardingState.currentStep < ONBOARDING_STEPS.length - 1) {
        saveOnboardingState({
          currentStep: onboardingState.currentStep + 1,
          completedSteps: newCompletedSteps,
        });
      } else {
        completeOnboarding();
      }
    }
  }, [onboardingState.currentStep, onboardingState.completedSteps, saveOnboardingState]);

  const previousStep = useCallback(() => {
    if (onboardingState.currentStep > 0) {
      setOnboardingState(prev => ({
        ...prev,
        currentStep: prev.currentStep - 1,
      }));
    }
  }, [onboardingState.currentStep]);

  const skipStep = useCallback(() => {
    const currentStepData = ONBOARDING_STEPS[onboardingState.currentStep];
    
    if (currentStepData) {
      const newSkippedSteps = [...onboardingState.skippedSteps, currentStepData.id];
      
      if (onboardingState.currentStep < ONBOARDING_STEPS.length - 1) {
        saveOnboardingState({
          currentStep: onboardingState.currentStep + 1,
          skippedSteps: newSkippedSteps,
        });
      } else {
        completeOnboarding();
      }
    }
  }, [onboardingState.currentStep, onboardingState.skippedSteps, saveOnboardingState]);

  const completeOnboarding = useCallback(() => {
    storage.set(`${STORAGE_KEY}-completed`, true);
    setOnboardingState(prev => ({
      ...prev,
      isActive: false,
    }));
    setIsOnboardingComplete(true);
  }, []);

  const resetOnboarding = useCallback(() => {
    storage.remove(STORAGE_KEY);
    storage.remove(`${STORAGE_KEY}-completed`);
    setOnboardingState({
      isFirstTime: true,
      currentStep: 0,
      isActive: false,
      completedSteps: [],
      skippedSteps: [],
    });
    setIsOnboardingComplete(false);
  }, []);

  const getCurrentStep = useCallback(() => {
    return ONBOARDING_STEPS[onboardingState.currentStep] || null;
  }, [onboardingState.currentStep]);

  const getProgress = useCallback(() => {
    return {
      current: onboardingState.currentStep + 1,
      total: ONBOARDING_STEPS.length,
      percentage: Math.round(((onboardingState.currentStep + 1) / ONBOARDING_STEPS.length) * 100),
    };
  }, [onboardingState.currentStep]);

  return {
    // State
    isFirstTime: onboardingState.isFirstTime,
    isActive: onboardingState.isActive,
    isComplete: isOnboardingComplete,
    currentStep: onboardingState.currentStep,
    
    // Data
    steps: ONBOARDING_STEPS,
    currentStepData: getCurrentStep(),
    progress: getProgress(),
    
    // Actions
    startOnboarding,
    nextStep,
    previousStep,
    skipStep,
    completeOnboarding,
    resetOnboarding,
  };
}

export default useFirstTimeUser;
