// Content Management System for Japanese Learning Platform
import type { J<PERSON>TLevel, LessonCategory, Lesson } from '@/types';

// Content structure for dynamic loading
export interface ContentStructure {
  levels: Record<JLPTLevel, LevelContent>;
}

export interface LevelContent {
  info: LevelInfo;
  categories: Record<LessonCategory, CategoryContent>;
}

export interface LevelInfo {
  level: JLPTLevel;
  title: string;
  description: string;
  totalLessons: number;
  estimatedHours: number;
  difficulty: string;
  skills: string[];
}

export interface CategoryContent {
  info: CategoryInfo;
  lessons: Lesson[];
}

export interface CategoryInfo {
  category: LessonCategory;
  name: string;
  description: string;
  icon: string;
  totalLessons: number;
  estimatedHours: number;
}

// Dynamic content loader
export class ContentLoader {
  private static instance: ContentLoader;
  private contentCache: Map<string, any> = new Map();

  static getInstance(): ContentLoader {
    if (!ContentLoader.instance) {
      ContentLoader.instance = new ContentLoader();
    }
    return ContentLoader.instance;
  }

  // Load level content dynamically
  async loadLevelContent(level: JLPTLevel): Promise<LevelContent | null> {
    const cacheKey = `level-${level}`;
    
    if (this.contentCache.has(cacheKey)) {
      return this.contentCache.get(cacheKey);
    }

    try {
      // Dynamic import of level content
      const content = await this.importLevelContent(level);
      this.contentCache.set(cacheKey, content);
      return content;
    } catch (error) {
      console.error(`Failed to load content for level ${level}:`, error);
      return null;
    }
  }

  // Load category content dynamically
  async loadCategoryContent(level: JLPTLevel, category: LessonCategory): Promise<CategoryContent | null> {
    const cacheKey = `${level}-${category}`;
    
    if (this.contentCache.has(cacheKey)) {
      return this.contentCache.get(cacheKey);
    }

    try {
      const content = await this.importCategoryContent(level, category);
      this.contentCache.set(cacheKey, content);
      return content;
    } catch (error) {
      console.error(`Failed to load content for ${level} ${category}:`, error);
      return null;
    }
  }

  // Load individual lesson
  async loadLesson(lessonId: string): Promise<Lesson | null> {
    const cacheKey = `lesson-${lessonId}`;
    
    if (this.contentCache.has(cacheKey)) {
      return this.contentCache.get(cacheKey);
    }

    try {
      const lesson = await this.importLesson(lessonId);
      this.contentCache.set(cacheKey, lesson);
      return lesson;
    } catch (error) {
      console.error(`Failed to load lesson ${lessonId}:`, error);
      return null;
    }
  }

  // Private methods for dynamic imports
  private async importLevelContent(level: JLPTLevel): Promise<LevelContent> {
    const module = await import(`./levels/${level.toLowerCase()}/index.ts`);
    return module.default || module.levelContent;
  }

  private async importCategoryContent(level: JLPTLevel, category: LessonCategory): Promise<CategoryContent> {
    const module = await import(`./levels/${level.toLowerCase()}/${category}.ts`);
    return module.default || module.categoryContent;
  }

  private async importLesson(lessonId: string): Promise<Lesson> {
    // Extract level and category from lesson ID (e.g., 'n5-grammar-1')
    const [levelPart, categoryPart] = lessonId.split('-');
    const level = levelPart.toUpperCase() as JLPTLevel;
    const category = categoryPart as LessonCategory;
    
    const module = await import(`./levels/${level.toLowerCase()}/${category}.ts`);
    const categoryContent = module.default || module.categoryContent;
    
    return categoryContent.lessons.find((lesson: Lesson) => lesson.id === lessonId);
  }

  // Clear cache (useful for development)
  clearCache(): void {
    this.contentCache.clear();
  }
}

// Export singleton instance
export const contentLoader = ContentLoader.getInstance();

// Fallback content for error states
export const fallbackLevelContent: LevelContent = {
  info: {
    level: 'N5',
    title: 'Content Not Available',
    description: 'This content is currently unavailable. Please try again later.',
    totalLessons: 0,
    estimatedHours: 0,
    difficulty: 'Unknown',
    skills: [],
  },
  categories: {} as Record<LessonCategory, CategoryContent>,
};

export const fallbackCategoryContent: CategoryContent = {
  info: {
    category: 'grammar',
    name: 'Content Not Available',
    description: 'This content is currently unavailable. Please try again later.',
    icon: 'AlertCircle',
    totalLessons: 0,
    estimatedHours: 0,
  },
  lessons: [],
};
