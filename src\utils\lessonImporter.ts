// Lesson Import and Export Utilities
import type { Lesson, JLPTLevel, LessonCategory } from '@/types';
import { validateLesson, sanitizeLesson } from './contentValidation';

export interface ImportResult {
  success: boolean;
  lesson?: Lesson;
  errors: string[];
  warnings: string[];
}

export interface BulkImportResult {
  totalProcessed: number;
  successful: number;
  failed: number;
  results: ImportResult[];
}

// Parse JSON lesson content
export const importLessonFromJSON = (jsonContent: string): ImportResult => {
  try {
    const rawLesson = JSON.parse(jsonContent);
    return importLessonFromObject(rawLesson);
  } catch (error) {
    return {
      success: false,
      errors: [`Invalid JSON format: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings: [],
    };
  }
};

// Parse YAML lesson content (basic implementation)
export const importLessonFromYAML = (yamlContent: string): ImportResult => {
  try {
    // Simple YAML parser for basic structure
    const lines = yamlContent.split('\n');
    const lesson: any = {};
    let currentSection: string | null = null;
    let currentArray: any[] = [];
    let currentObject: any = {};
    let indentLevel = 0;

    for (const line of lines) {
      const trimmed = line.trim();
      if (!trimmed || trimmed.startsWith('#')) continue;

      const indent = line.length - line.trimStart().length;
      
      if (trimmed.includes(':') && !trimmed.startsWith('-')) {
        const [key, value] = trimmed.split(':').map(s => s.trim());
        
        if (value) {
          // Simple key-value pair
          if (value.startsWith('"') && value.endsWith('"')) {
            lesson[key] = value.slice(1, -1);
          } else if (!isNaN(Number(value))) {
            lesson[key] = Number(value);
          } else {
            lesson[key] = value;
          }
        } else {
          // Start of array or object
          currentSection = key;
          if (key === 'tags' || key === 'content') {
            currentArray = [];
            lesson[key] = currentArray;
          }
        }
      } else if (trimmed.startsWith('-')) {
        // Array item
        const value = trimmed.substring(1).trim();
        if (value.startsWith('"') && value.endsWith('"')) {
          currentArray.push(value.slice(1, -1));
        } else {
          currentArray.push(value);
        }
      }
    }

    return importLessonFromObject(lesson);
  } catch (error) {
    return {
      success: false,
      errors: [`Invalid YAML format: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings: [],
    };
  }
};

// Import lesson from parsed object
export const importLessonFromObject = (rawLesson: any): ImportResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Validate the lesson structure
    const validation = validateLesson(rawLesson);
    
    if (!validation.isValid) {
      return {
        success: false,
        errors: validation.errors.map(e => `${e.field}: ${e.message}`),
        warnings: validation.warnings.map(w => `${w.field}: ${w.message}`),
      };
    }

    // Sanitize and create the lesson
    const lesson = sanitizeLesson(rawLesson);

    return {
      success: true,
      lesson,
      errors: [],
      warnings: validation.warnings.map(w => `${w.field}: ${w.message}`),
    };
  } catch (error) {
    return {
      success: false,
      errors: [`Failed to process lesson: ${error instanceof Error ? error.message : 'Unknown error'}`],
      warnings,
    };
  }
};

// Bulk import lessons from multiple files
export const bulkImportLessons = (files: { name: string; content: string; type: 'json' | 'yaml' }[]): BulkImportResult => {
  const results: ImportResult[] = [];
  let successful = 0;
  let failed = 0;

  for (const file of files) {
    let result: ImportResult;
    
    if (file.type === 'json') {
      result = importLessonFromJSON(file.content);
    } else {
      result = importLessonFromYAML(file.content);
    }

    // Add file context to errors
    if (!result.success) {
      result.errors = result.errors.map(error => `${file.name}: ${error}`);
    }
    if (result.warnings.length > 0) {
      result.warnings = result.warnings.map(warning => `${file.name}: ${warning}`);
    }

    results.push(result);
    
    if (result.success) {
      successful++;
    } else {
      failed++;
    }
  }

  return {
    totalProcessed: files.length,
    successful,
    failed,
    results,
  };
};

// Generate lesson template
export const generateLessonTemplate = (
  level: JLPTLevel = 'N5',
  category: LessonCategory = 'grammar'
): Lesson => {
  const timestamp = new Date();
  const id = `${level.toLowerCase()}-${category}-${Date.now()}`;

  return {
    id,
    title: 'New Lesson Title',
    description: 'Describe what students will learn in this lesson',
    level,
    category,
    difficulty: 1,
    estimatedTime: 15,
    tags: ['new-lesson'],
    content: [
      {
        id: 'intro-text',
        type: 'text',
        order: 1,
        data: {
          text: 'Welcome to this lesson!',
          translation: 'このレッスンへようこそ！',
          notes: 'This is an introductory text block.',
        },
      },
      {
        id: 'example-quiz',
        type: 'quiz',
        order: 2,
        data: {
          questions: [
            {
              id: 'q1',
              type: 'multiple-choice',
              question: 'What is the correct answer?',
              options: ['Option A', 'Option B', 'Option C', 'Option D'],
              correctAnswer: 'Option A',
              points: 10,
              explanation: 'This is why Option A is correct.',
            },
          ],
          passingScore: 70,
        },
      },
    ],
    createdAt: timestamp,
    updatedAt: timestamp,
  };
};

// Export lesson collection to ZIP-like structure
export const exportLessonCollection = (lessons: Lesson[]): { [filename: string]: string } => {
  const files: { [filename: string]: string } = {};

  // Group lessons by level and category
  const grouped: { [key: string]: Lesson[] } = {};
  
  lessons.forEach(lesson => {
    const key = `${lesson.level}/${lesson.category}`;
    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(lesson);
  });

  // Create files for each group
  Object.entries(grouped).forEach(([path, groupLessons]) => {
    const content = {
      info: {
        level: groupLessons[0].level,
        category: groupLessons[0].category,
        totalLessons: groupLessons.length,
        exportedAt: new Date().toISOString(),
      },
      lessons: groupLessons,
    };

    files[`${path}/lessons.json`] = JSON.stringify(content, null, 2);
  });

  // Create index file
  const index = {
    exportedAt: new Date().toISOString(),
    totalLessons: lessons.length,
    structure: Object.keys(grouped).reduce((acc, path) => {
      acc[path] = grouped[path].length;
      return acc;
    }, {} as { [key: string]: number }),
  };

  files['index.json'] = JSON.stringify(index, null, 2);

  return files;
};

// Validate lesson ID format
export const validateLessonId = (id: string): { valid: boolean; message?: string } => {
  if (!id || typeof id !== 'string') {
    return { valid: false, message: 'ID must be a non-empty string' };
  }

  if (id.length < 3) {
    return { valid: false, message: 'ID must be at least 3 characters long' };
  }

  if (!/^[a-z0-9-]+$/.test(id)) {
    return { valid: false, message: 'ID can only contain lowercase letters, numbers, and hyphens' };
  }

  if (id.startsWith('-') || id.endsWith('-')) {
    return { valid: false, message: 'ID cannot start or end with a hyphen' };
  }

  if (id.includes('--')) {
    return { valid: false, message: 'ID cannot contain consecutive hyphens' };
  }

  return { valid: true };
};

// Generate unique lesson ID
export const generateLessonId = (title: string, level: JLPTLevel, category: LessonCategory): string => {
  const baseId = title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

  const prefix = `${level.toLowerCase()}-${category}`;
  const timestamp = Date.now().toString().slice(-6); // Last 6 digits of timestamp
  
  return `${prefix}-${baseId}-${timestamp}`;
};
