// Redesigned Dashboard - Modern, Clean, Interaction-Focused with UX Enhancements
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Button,
  Badge,
  Progress,
} from '@/components/ui';
import {
  CardModern,
  CardModernHeader,
  CardModernTitle,
  CardModernDescription,
  CardModernContent,
  StatsCard,
  ProgressCard,
  HeroCard,
} from '@/components/ui/CardModern';
import {
  BookOpen,
  PenTool,
  Type,
  Headphones,
  Play,
  TrendingUp,
  Calendar,
  Target,
  ChevronRight,
  Star,
  Flame,
  Award,
  Clock,
  ArrowRight,
  Zap,
  Filter,
  Eye,
} from 'lucide-react';
import { DarkModeToggle } from '@/components/ui/DarkModeToggle';
import { MetricModal, type MetricData, type MetricType } from '@/components/MetricModal';
import { useLessonTags, type LessonStatus, LESSON_TAG_CONFIGS } from '@/hooks/useLessonTags';
import { useFirstTimeUser } from '@/hooks/useFirstTimeUser';
import { useAppStore } from '@/stores/useAppStore';
import { JLPT_LEVEL_INFO, CATEGORY_INFO } from '@/utils';
import type { JLPTLevel, LessonCategory, Lesson } from '@/types';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut',
    },
  },
};

const progressVariants = {
  hidden: { width: 0 },
  visible: (progress: number) => ({
    width: `${progress}%`,
    transition: {
      duration: 1.5,
      ease: 'easeOut',
      delay: 0.5,
    },
  }),
};

export const RedesignDashboard: React.FC = () => {
  const navigate = useNavigate();
  const { user, currentLevel } = useAppStore();
  const { setCurrentLevel } = useAppStore();
  const [activeTab, setActiveTab] = useState<'categories' | 'continue'>('continue');
  const [selectedMetric, setSelectedMetric] = useState<MetricData | null>(null);
  const [isMetricModalOpen, setIsMetricModalOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<LessonStatus | 'all'>('all');

  // Onboarding hook
  const { isActive: isOnboardingActive } = useFirstTimeUser();

  // Enhanced mock data with more realistic metrics
  const levelProgress = {
    N5: 85,
    N4: 60,
    N3: 30,
    N2: 10,
    N1: 0,
  };

  const todayStats = {
    studyTime: 25,
    lessonsCompleted: 3,
    streak: 7,
    weeklyGoal: 150,
    weeklyProgress: 120,
    xpEarned: 240,
    perfectDays: 5,
  };

  // Mock lessons data for lesson tagging system
  const mockLessons: Lesson[] = [
    {
      id: '1',
      title: 'Basic Greetings',
      description: 'Learn essential Japanese greetings',
      category: 'vocabulary' as LessonCategory,
      level: 'N5' as JLPTLevel,
      difficulty: 1,
      estimatedTime: 5,
      tags: ['beginner', 'daily-use'],
      content: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      title: 'Present Tense Verbs',
      description: 'Master Japanese present tense verb conjugation',
      category: 'grammar' as LessonCategory,
      level: 'N5' as JLPTLevel,
      difficulty: 2,
      estimatedTime: 8,
      tags: ['grammar', 'verbs'],
      content: [],
      prerequisites: ['1'],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      title: 'Numbers 1-100',
      description: 'Learn to count in Japanese',
      category: 'vocabulary' as LessonCategory,
      level: 'N5' as JLPTLevel,
      difficulty: 1,
      estimatedTime: 12,
      tags: ['numbers', 'practical'],
      content: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '4',
      title: 'Hiragana Practice',
      description: 'Practice reading hiragana characters',
      category: 'reading' as LessonCategory,
      level: 'N5' as JLPTLevel,
      difficulty: 2,
      estimatedTime: 15,
      tags: ['hiragana', 'reading'],
      content: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  // Use lesson tagging hook
  const { lessonsWithTags, filterByStatus, getRecommendedLessons, getStatusCounts, tagConfigs } = useLessonTags(mockLessons);

  // Metric modal handlers
  const handleMetricClick = useCallback((type: MetricType) => {
    const metricData: MetricData = {
      type,
      current: type === 'studyTime' ? `${todayStats.studyTime}m` :
               type === 'lessons' ? todayStats.lessonsCompleted :
               type === 'streak' ? `${todayStats.streak} days` :
               todayStats.xpEarned,
      label: type === 'studyTime' ? 'Study Time' :
             type === 'lessons' ? 'Lessons Completed' :
             type === 'streak' ? 'Study Streak' :
             'XP Earned',
      icon: type === 'studyTime' ? <Clock className="w-5 h-5" /> :
            type === 'lessons' ? <BookOpen className="w-5 h-5" /> :
            type === 'streak' ? <Flame className="w-5 h-5" /> :
            <Zap className="w-5 h-5" />,
      breakdown: {
        today: type === 'studyTime' ? todayStats.studyTime :
               type === 'lessons' ? todayStats.lessonsCompleted :
               type === 'streak' ? todayStats.streak :
               todayStats.xpEarned,
        thisWeek: type === 'studyTime' ? 180 :
                  type === 'lessons' ? 15 :
                  type === 'streak' ? todayStats.streak :
                  1200,
        thisMonth: type === 'studyTime' ? 720 :
                   type === 'lessons' ? 45 :
                   type === 'streak' ? todayStats.streak :
                   4800,
        allTime: type === 'studyTime' ? 2400 :
                 type === 'lessons' ? 120 :
                 type === 'streak' ? todayStats.streak :
                 15600,
        best: type === 'studyTime' ? 90 :
              type === 'lessons' ? 8 :
              type === 'streak' ? 21 :
              480,
      },
      insights: type === 'studyTime' ? [
        'You\'re studying 15% more than last week!',
        'Your average session is 8 minutes',
        'Best study time: 7-9 PM'
      ] : type === 'lessons' ? [
        'You completed 50% more lessons than yesterday',
        'Grammar lessons are your strongest area',
        'Try mixing vocabulary with grammar for better retention'
      ] : type === 'streak' ? [
        'You\'re on fire! 🔥',
        'Your longest streak was 21 days',
        'Consistency is key to language learning'
      ] : [
        'You earned 20% more XP than yesterday',
        'Vocabulary lessons give the most XP',
        'You\'re in the top 15% of learners this week'
      ],
      goals: type === 'studyTime' ? {
        daily: 30,
        weekly: 210,
        monthly: 900,
      } : type === 'lessons' ? {
        daily: 5,
        weekly: 35,
      } : undefined,
    };

    setSelectedMetric(metricData);
    setIsMetricModalOpen(true);
  }, [todayStats]);

  const closeMetricModal = useCallback(() => {
    setIsMetricModalOpen(false);
    setSelectedMetric(null);
  }, []);

  // Filter lessons by status
  const filteredLessons = statusFilter === 'all' ? lessonsWithTags : filterByStatus(statusFilter);
  const recommendedLessons = getRecommendedLessons(3);
  const statusCounts = getStatusCounts();

  const categories = [
    { key: 'grammar' as LessonCategory, icon: PenTool, lessons: 45, completed: 12, color: 'bg-primary-500' },
    { key: 'vocabulary' as LessonCategory, icon: BookOpen, lessons: 120, completed: 89, color: 'bg-secondary-500' },
    { key: 'kanji' as LessonCategory, icon: Type, lessons: 80, completed: 25, color: 'bg-tertiary-500' },
    { key: 'listening' as LessonCategory, icon: Headphones, lessons: 35, completed: 8, color: 'bg-primary-400' },
  ];

  const currentLevelInfo = JLPT_LEVEL_INFO[currentLevel];
  const overallProgress = Math.round(
    Object.values(levelProgress).reduce((acc, val) => acc + val, 0) / Object.keys(levelProgress).length
  );

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50/30 dark:from-neutral-950 dark:via-neutral-900 dark:to-primary-950/30"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header Section - Welcome & Quick Actions */}
      <motion.section
        className="relative overflow-hidden"
        variants={itemVariants}
      >
        <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 via-secondary-500/10 to-tertiary-500/10 dark:from-primary-500/5 dark:via-secondary-500/5 dark:to-tertiary-500/5" />
        <div className="relative px-6 py-8 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between space-y-6 lg:space-y-0">
              {/* Welcome Block */}
              <div className="space-y-2" data-onboarding="welcome-section">
                <motion.div
                  className="flex items-center space-x-3"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    {currentLevel}
                  </div>
                  <div>
                    <h1 className="text-3xl lg:text-4xl font-bold text-neutral-900 dark:text-white">
                      Welcome back{user ? `, ${user.username}` : ''}!
                    </h1>
                    <p className="text-lg text-neutral-600 dark:text-neutral-300">
                      Ready to continue your Japanese journey?
                    </p>
                  </div>
                </motion.div>
                
                {/* Quick Stats */}
                <motion.div
                  className="flex items-center space-x-6 text-sm"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <div className="flex items-center space-x-2 text-tertiary-600 dark:text-tertiary-400">
                    <Flame className="w-4 h-4" />
                    <span className="font-medium">{todayStats.streak} day streak</span>
                  </div>
                  <div className="flex items-center space-x-2 text-secondary-600 dark:text-secondary-400">
                    <Star className="w-4 h-4" />
                    <span className="font-medium">{todayStats.xpEarned} XP today</span>
                  </div>
                  <div className="flex items-center space-x-2 text-primary-600 dark:text-primary-400">
                    <Award className="w-4 h-4" />
                    <span className="font-medium">{overallProgress}% overall</span>
                  </div>
                </motion.div>
              </div>

              {/* Quick Actions */}
              <motion.div
                className="flex items-center space-x-4"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.4 }}
              >
                <DarkModeToggle />
                <Button size="lg" className="bg-gradient-to-r from-primary-500 to-secondary-500 hover:from-primary-600 hover:to-secondary-600 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                  <Play className="mr-2 h-5 w-5" />
                  Continue Learning
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.section>

      <div className="max-w-[1600px] mx-auto px-6 lg:px-8 pb-12" data-onboarding="main-content">
        <div className="grid grid-cols-1 lg:grid-cols-[2fr_3fr] xl:grid-cols-[1fr_2fr] gap-8">
          {/* Main Content Area */}
          <div className="space-y-8">
            {/* Today's Performance - Redesigned Stats */}
            <motion.section variants={itemVariants} data-onboarding="stats-section">
              <div className="mb-6">
                <h2 className="text-2xl font-bold text-neutral-900 dark:text-white mb-2">Today's Performance</h2>
                <p className="text-neutral-600 dark:text-neutral-400">Track your daily learning progress</p>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                <button
                  onClick={() => handleMetricClick('studyTime')}
                  className="text-left transition-all duration-200 hover:scale-105 active:scale-95"
                >
                  <StatsCard
                    icon={<Clock className="w-5 h-5" />}
                    label="Study Time"
                    value={`${todayStats.studyTime}m`}
                    trend={{ value: 15, isPositive: true }}
                  />
                </button>
                <button
                  onClick={() => handleMetricClick('lessons')}
                  className="text-left transition-all duration-200 hover:scale-105 active:scale-95"
                >
                  <StatsCard
                    icon={<BookOpen className="w-5 h-5" />}
                    label="Lessons"
                    value={todayStats.lessonsCompleted}
                    trend={{ value: 50, isPositive: true }}
                  />
                </button>
                <button
                  onClick={() => handleMetricClick('streak')}
                  className="text-left transition-all duration-200 hover:scale-105 active:scale-95"
                >
                  <StatsCard
                    icon={<Flame className="w-5 h-5" />}
                    label="Streak"
                    value={`${todayStats.streak} days`}
                  />
                </button>
                <button
                  onClick={() => handleMetricClick('xp')}
                  className="text-left transition-all duration-200 hover:scale-105 active:scale-95"
                >
                  <StatsCard
                    icon={<Zap className="w-5 h-5" />}
                    label="XP Earned"
                    value={todayStats.xpEarned}
                    trend={{ value: 20, isPositive: true }}
                  />
                </button>
              </div>
            </motion.section>

            {/* Hero Section - Continue Learning */}
            <motion.section variants={itemVariants}>
              <HeroCard
                title="Continue Your Journey"
                description="Pick up where you left off and keep building your Japanese skills"
                action={
                  <Button
                    size="lg"
                    variant="secondary"
                    className="shadow-lg animate-pulse"
                    data-onboarding="continue-button"
                    onClick={() => navigate('/lesson/2')}
                  >
                    <ArrowRight className="ml-2 h-5 w-5" />
                    Start Learning
                  </Button>
                }
                className="mb-6"
              >
                <div className="flex items-center space-x-4">
                  <div className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">
                    {Math.round((todayStats.weeklyProgress / todayStats.weeklyGoal) * 100)}%
                  </div>
                  <div className="text-sm text-neutral-600 dark:text-neutral-400">
                    of weekly goal completed
                  </div>
                </div>
              </HeroCard>
            </motion.section>

            {/* Tabbed Content - Categories & Continue Learning */}
            <motion.section variants={itemVariants}>
              <div className="space-y-6">
                {/* Tab Navigation */}
                <div className="flex items-center space-x-1 bg-neutral-100 dark:bg-neutral-800 p-1 rounded-xl w-fit" data-onboarding="tab-navigation">
                  <button
                    onClick={() => setActiveTab('continue')}
                    className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeTab === 'continue'
                        ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-white shadow-sm'
                        : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white'
                    }`}
                  >
                    Continue Learning
                  </button>
                  <button
                    onClick={() => setActiveTab('categories')}
                    className={`px-6 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                      activeTab === 'categories'
                        ? 'bg-white dark:bg-neutral-700 text-neutral-900 dark:text-white shadow-sm'
                        : 'text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white'
                    }`}
                  >
                    Categories
                  </button>
                </div>

                {/* Tab Content */}
                <AnimatePresence mode="wait">
                  {activeTab === 'continue' && (
                    <motion.div
                      key="continue"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-6"
                    >
                      {/* Status Filter */}
                      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
                        <button
                          onClick={() => setStatusFilter('all')}
                          className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                            statusFilter === 'all'
                              ? 'bg-primary-100 text-primary-700 dark:bg-primary-900/30 dark:text-primary-300'
                              : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200 dark:bg-neutral-800 dark:text-neutral-400 dark:hover:bg-neutral-700'
                          }`}
                        >
                          <Eye className="w-3 h-3 mr-1 inline" />
                          All ({lessonsWithTags.length})
                        </button>
                        {Object.entries(statusCounts).map(([status, count]) => {
                          const config = tagConfigs[status as LessonStatus];
                          return (
                            <button
                              key={status}
                              onClick={() => setStatusFilter(status as LessonStatus)}
                              className={`px-3 py-1 rounded-full text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                                statusFilter === status
                                  ? `${config.bgColor} ${config.color}`
                                  : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200 dark:bg-neutral-800 dark:text-neutral-400 dark:hover:bg-neutral-700'
                              }`}
                            >
                              <span className="mr-1">{config.icon}</span>
                              {config.label} ({count})
                            </button>
                          );
                        })}
                      </div>

                      {/* Lessons Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredLessons.map((lesson, index) => {
                          const categoryInfo = CATEGORY_INFO[lesson.category];
                          const tagConfig = tagConfigs[lesson.status];
                          return (
                            <motion.div
                              key={lesson.id}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              data-onboarding={index === 0 ? "lesson-card" : undefined}
                            >
                              <CardModern
                                variant="interactive-glass"
                                glow="subtle"
                                className="group h-full transition-all duration-200 hover:scale-105 active:scale-95"
                                onClick={() => navigate(`/lesson/${lesson.id}`)}
                              >
                                <CardModernHeader spacing="sm">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                      <Badge variant="outline" className="text-xs">
                                        {lesson.level}
                                      </Badge>
                                      {/* Status Tag */}
                                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${tagConfig.bgColor} ${tagConfig.color} flex items-center space-x-1`}>
                                        <span>{tagConfig.icon}</span>
                                        <span>{tagConfig.label}</span>
                                      </div>
                                    </div>
                                    <div className="flex items-center space-x-1 text-xs text-neutral-500">
                                      <Clock className="w-3 h-3" />
                                      <span>{lesson.estimatedTime}m</span>
                                    </div>
                                  </div>
                                  <CardModernTitle className="text-lg group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                                    {lesson.title}
                                  </CardModernTitle>
                                  <CardModernDescription>
                                    {lesson.description}
                                  </CardModernDescription>
                                </CardModernHeader>
                                <CardModernContent>
                                  <div className="space-y-3">
                                    <div className="flex items-center justify-between">
                                      <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                                        {categoryInfo.name} • Level {lesson.difficulty}
                                      </span>
                                      <ChevronRight className="w-4 h-4 text-neutral-400 group-hover:text-primary-500 transition-colors" />
                                    </div>
                                    {lesson.isRecommended && (
                                      <div className="flex items-center space-x-1 text-xs text-primary-600 dark:text-primary-400">
                                        <Star className="w-3 h-3 fill-current" />
                                        <span>Recommended for you</span>
                                      </div>
                                    )}
                                  </div>
                                </CardModernContent>
                              </CardModern>
                            </motion.div>
                          );
                        })}
                      </div>
                    </motion.div>
                  )}

                  {activeTab === 'categories' && (
                    <motion.div
                      key="categories"
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.3 }}
                      className="grid grid-cols-1 md:grid-cols-2 gap-6"
                    >
                      {categories.map((category, index) => {
                        const Icon = category.icon;
                        const categoryInfo = CATEGORY_INFO[category.key];
                        const completionRate = Math.round((category.completed / category.lessons) * 100);
                        
                        return (
                          <motion.div
                            key={category.key}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <CardModern
                              variant="interactive"
                              glow="subtle"
                              className="group"
                              onClick={() => navigate(`/level/${currentLevel.toLowerCase()}/category/${category.key}`)}
                            >
                              <CardModernHeader>
                                <div className="flex items-center space-x-4">
                                  <div className={`p-3 rounded-2xl ${category.color} text-white group-hover:scale-110 transition-transform duration-200`}>
                                    <Icon className="h-6 w-6" />
                                  </div>
                                  <div className="flex-1">
                                    <CardModernTitle className="text-xl group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                                      {categoryInfo.name}
                                    </CardModernTitle>
                                    <CardModernDescription>
                                      {category.completed}/{category.lessons} lessons completed
                                    </CardModernDescription>
                                  </div>
                                  <ChevronRight className="w-5 h-5 text-neutral-400 group-hover:text-primary-500 transition-colors" />
                                </div>
                              </CardModernHeader>
                              <CardModernContent>
                                <div className="space-y-3">
                                  <div className="flex justify-between items-center">
                                    <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
                                      Completion
                                    </span>
                                    <span className="text-sm font-bold text-neutral-900 dark:text-neutral-100">
                                      {completionRate}%
                                    </span>
                                  </div>
                                  <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                                    <motion.div
                                      className={`h-full ${category.color} rounded-full`}
                                      variants={progressVariants}
                                      initial="hidden"
                                      animate="visible"
                                      custom={completionRate}
                                    />
                                  </div>
                                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                                    {categoryInfo.description}
                                  </p>
                                </div>
                              </CardModernContent>
                            </CardModern>
                          </motion.div>
                        );
                      })}
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.section>
          </div>

          {/* Sidebar - JLPT Progress Tracker */}
          <motion.aside
            className="space-y-6"
            variants={itemVariants}
            data-onboarding="jlpt-sidebar"
          >
            <div className="sticky top-6 space-y-6 bg-white/50 dark:bg-neutral-800/50 backdrop-blur-md rounded-2xl p-6 border border-neutral-200/50 dark:border-neutral-700/50">
              <div>
                <h3 className="text-xl font-bold text-neutral-900 dark:text-white mb-2">JLPT Progress</h3>
                <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
                  Track your progress across all levels
                </p>
              </div>
              
              <div className="space-y-4">
                {(Object.keys(levelProgress) as JLPTLevel[]).map((level, index) => {
                  const levelInfo = JLPT_LEVEL_INFO[level];
                  const progress = levelProgress[level];
                  const isActive = level === currentLevel;
                  
                  return (
                    <motion.div
                      key={level}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.6 + index * 0.1 }}
                    >
                      <ProgressCard
                        title={level}
                        description={levelInfo.description}
                        progress={progress}
                        level={level}
                        isActive={isActive}
                        onClick={() => {
                          setCurrentLevel(level);
                          navigate(`/level/${level.toLowerCase()}`);
                        }}
                      />
                    </motion.div>
                  );
                })}
              </div>
              
              {/* Weekly Goal Progress */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2 }}
              >
                <CardModern variant="glass" className="p-4">
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <h4 className="font-semibold text-neutral-900 dark:text-white">Weekly Goal</h4>
                      <Target className="w-4 h-4 text-primary-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-neutral-600 dark:text-neutral-400">
                          {todayStats.weeklyProgress}m / {todayStats.weeklyGoal}m
                        </span>
                        <span className="font-medium text-neutral-900 dark:text-neutral-100">
                          {Math.round((todayStats.weeklyProgress / todayStats.weeklyGoal) * 100)}%
                        </span>
                      </div>
                      <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                        <motion.div
                          className="h-full bg-gradient-to-r from-tertiary-500 to-secondary-500 rounded-full"
                          variants={progressVariants}
                          initial="hidden"
                          animate="visible"
                          custom={(todayStats.weeklyProgress / todayStats.weeklyGoal) * 100}
                        />
                      </div>
                    </div>
                  </div>
                </CardModern>
              </motion.div>
            </div>
          </motion.aside>
        </div>
      </div>

      {/* Metric Modal */}
      <MetricModal
        isOpen={isMetricModalOpen}
        onClose={closeMetricModal}
        metric={selectedMetric}
      />
    </motion.div>
  );
};
