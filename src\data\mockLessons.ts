import type { Lesson, <PERSON><PERSON>TL<PERSON>l, LessonCategory } from '@/types';

// Mock lesson data for the Japanese learning platform
export const mockLessons: Lesson[] = [
  // N5 Grammar Lessons
  {
    id: 'n5-grammar-1',
    title: 'Basic Sentence Structure',
    description: 'Learn the fundamental structure of Japanese sentences',
    level: 'N5',
    category: 'grammar',
    difficulty: 1,
    estimatedTime: 20,
    tags: ['sentence', 'structure', 'basic'],
    content: [
      {
        id: 'content-1',
        type: 'text',
        order: 1,
        data: {
          text: 'Japanese sentences follow a Subject-Object-Verb (SOV) structure.',
          translation: 'Japanese sentences follow a Subject-Object-Verb (SOV) structure.',
        },
      },
    ],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 'n5-grammar-2',
    title: 'Present Tense Verbs',
    description: 'Master present tense verb conjugations',
    level: 'N5',
    category: 'grammar',
    difficulty: 2,
    estimatedTime: 25,
    tags: ['verbs', 'present', 'conjugation'],
    content: [
      {
        id: 'content-2',
        type: 'text',
        order: 1,
        data: {
          text: 'Japanese verbs in present tense end with -masu in polite form.',
          translation: 'Japanese verbs in present tense end with -masu in polite form.',
        },
      },
    ],
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02'),
  },

  // N5 Vocabulary Lessons
  {
    id: 'n5-vocab-1',
    title: 'Basic Greetings',
    description: 'Essential Japanese greetings for daily conversations',
    level: 'N5',
    category: 'vocabulary',
    difficulty: 1,
    estimatedTime: 15,
    tags: ['greetings', 'daily', 'conversation'],
    content: [
      {
        id: 'content-3',
        type: 'text',
        order: 1,
        data: {
          text: 'おはよう',
          furigana: 'おはよう',
          translation: 'Good morning (casual)',
        },
      },
    ],
    createdAt: new Date('2024-01-03'),
    updatedAt: new Date('2024-01-03'),
  },
  {
    id: 'n5-vocab-2',
    title: 'Family Members',
    description: 'Learn vocabulary for family relationships',
    level: 'N5',
    category: 'vocabulary',
    difficulty: 2,
    estimatedTime: 18,
    tags: ['family', 'relationships', 'people'],
    content: [
      {
        id: 'content-4',
        type: 'text',
        order: 1,
        data: {
          text: 'かぞく',
          furigana: 'かぞく',
          translation: 'family',
        },
      },
    ],
    createdAt: new Date('2024-01-04'),
    updatedAt: new Date('2024-01-04'),
  },

  // N5 Kanji Lessons
  {
    id: 'n5-kanji-1',
    title: 'Numbers 1-10',
    description: 'Learn the kanji for basic numbers',
    level: 'N5',
    category: 'kanji',
    difficulty: 1,
    estimatedTime: 30,
    tags: ['numbers', 'kanji', 'basic'],
    content: [
      {
        id: 'content-5',
        type: 'text',
        order: 1,
        data: {
          text: '一',
          furigana: 'いち',
          translation: 'one',
        },
      },
    ],
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05'),
  },

  // N4 Lessons
  {
    id: 'n4-grammar-1',
    title: 'Past Tense Forms',
    description: 'Learn how to express past actions in Japanese',
    level: 'N4',
    category: 'grammar',
    difficulty: 3,
    estimatedTime: 35,
    tags: ['past', 'tense', 'verbs'],
    content: [
      {
        id: 'content-6',
        type: 'text',
        order: 1,
        data: {
          text: 'Past tense verbs end with -mashita in polite form.',
          translation: 'Past tense verbs end with -mashita in polite form.',
        },
      },
    ],
    createdAt: new Date('2024-01-06'),
    updatedAt: new Date('2024-01-06'),
  },
  {
    id: 'n4-vocab-1',
    title: 'Weather and Seasons',
    description: 'Vocabulary related to weather and seasonal changes',
    level: 'N4',
    category: 'vocabulary',
    difficulty: 2,
    estimatedTime: 22,
    tags: ['weather', 'seasons', 'nature'],
    content: [
      {
        id: 'content-7',
        type: 'text',
        order: 1,
        data: {
          text: 'てんき',
          furigana: 'てんき',
          translation: 'weather',
        },
      },
    ],
    createdAt: new Date('2024-01-07'),
    updatedAt: new Date('2024-01-07'),
  },

  // N3 Lessons
  {
    id: 'n3-grammar-1',
    title: 'Conditional Forms',
    description: 'Express conditions and hypothetical situations',
    level: 'N3',
    category: 'grammar',
    difficulty: 4,
    estimatedTime: 40,
    tags: ['conditional', 'if', 'hypothetical'],
    content: [
      {
        id: 'content-8',
        type: 'text',
        order: 1,
        data: {
          text: 'Conditional forms express "if" statements in Japanese.',
          translation: 'Conditional forms express "if" statements in Japanese.',
        },
      },
    ],
    createdAt: new Date('2024-01-08'),
    updatedAt: new Date('2024-01-08'),
  },
];

// Helper functions to filter lessons
export const getLessonsByLevel = (level: JLPTLevel): Lesson[] => {
  return mockLessons.filter(lesson => lesson.level === level);
};

export const getLessonsByCategory = (
  level: JLPTLevel,
  category: LessonCategory
): Lesson[] => {
  return mockLessons.filter(
    lesson => lesson.level === level && lesson.category === category
  );
};

export const getLessonById = (id: string): Lesson | undefined => {
  return mockLessons.find(lesson => lesson.id === id);
};

export const getRecentLessons = (limit: number = 5): Lesson[] => {
  return mockLessons
    .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime())
    .slice(0, limit);
};

// Mock progress data
export const mockProgress = {
  N5: {
    totalLessons: 120,
    completedLessons: 102,
    categories: {
      grammar: { total: 30, completed: 28 },
      vocabulary: { total: 50, completed: 45 },
      kanji: { total: 25, completed: 20 },
      reading: { total: 10, completed: 7 },
      listening: { total: 5, completed: 2 },
    },
  },
  N4: {
    totalLessons: 150,
    completedLessons: 90,
    categories: {
      grammar: { total: 40, completed: 25 },
      vocabulary: { total: 60, completed: 35 },
      kanji: { total: 30, completed: 18 },
      reading: { total: 15, completed: 10 },
      listening: { total: 5, completed: 2 },
    },
  },
  N3: {
    totalLessons: 180,
    completedLessons: 54,
    categories: {
      grammar: { total: 50, completed: 15 },
      vocabulary: { total: 70, completed: 20 },
      kanji: { total: 40, completed: 12 },
      reading: { total: 15, completed: 5 },
      listening: { total: 5, completed: 2 },
    },
  },
  N2: {
    totalLessons: 200,
    completedLessons: 20,
    categories: {
      grammar: { total: 60, completed: 5 },
      vocabulary: { total: 80, completed: 8 },
      kanji: { total: 40, completed: 4 },
      reading: { total: 15, completed: 2 },
      listening: { total: 5, completed: 1 },
    },
  },
  N1: {
    totalLessons: 250,
    completedLessons: 0,
    categories: {
      grammar: { total: 70, completed: 0 },
      vocabulary: { total: 100, completed: 0 },
      kanji: { total: 50, completed: 0 },
      reading: { total: 20, completed: 0 },
      listening: { total: 10, completed: 0 },
    },
  },
};
