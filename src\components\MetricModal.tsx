import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, TrendingUp, Calendar, Award, Flame, Clock, Zap, Target } from 'lucide-react';
import { Button } from '@/components/ui';

export type MetricType = 'studyTime' | 'lessons' | 'streak' | 'xp';

export interface MetricData {
  type: MetricType;
  current: number | string;
  label: string;
  icon: React.ReactNode;
  breakdown: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    allTime: number;
    best?: number;
  };
  insights: string[];
  goals?: {
    daily?: number;
    weekly?: number;
    monthly?: number;
  };
}

interface MetricModalProps {
  isOpen: boolean;
  onClose: () => void;
  metric: MetricData | null;
}

const modalVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 20,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      damping: 25,
      stiffness: 300,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: 20,
    transition: {
      duration: 0.2,
    },
  },
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
};

export function MetricModal({ isOpen, onClose, metric }: MetricModalProps) {
  if (!metric) return null;

  const formatValue = (value: number, type: MetricType) => {
    switch (type) {
      case 'studyTime':
        return value < 60 ? `${value}m` : `${Math.floor(value / 60)}h ${value % 60}m`;
      case 'xp':
        return value.toLocaleString();
      case 'streak':
        return `${value} day${value !== 1 ? 's' : ''}`;
      case 'lessons':
        return value.toString();
      default:
        return value.toString();
    }
  };

  const getProgressPercentage = (current: number, goal?: number) => {
    if (!goal) return 0;
    return Math.min((current / goal) * 100, 100);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Overlay */}
          <motion.div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            variants={overlayVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            className="relative w-full max-w-md bg-white dark:bg-neutral-800 rounded-2xl shadow-2xl overflow-hidden"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-neutral-200 dark:border-neutral-700">
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-primary-100 dark:bg-primary-900/30 rounded-lg text-primary-600 dark:text-primary-400">
                  {metric.icon}
                </div>
                <div>
                  <h2 className="text-xl font-bold text-neutral-900 dark:text-white">
                    {metric.label}
                  </h2>
                  <p className="text-sm text-neutral-600 dark:text-neutral-400">
                    Detailed breakdown and insights
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Content */}
            <div className="p-6 space-y-6">
              {/* Current Value */}
              <div className="text-center">
                <div className="text-3xl font-bold text-neutral-900 dark:text-white mb-2">
                  {typeof metric.current === 'string' ? metric.current : formatValue(metric.current as number, metric.type)}
                </div>
                <p className="text-sm text-neutral-600 dark:text-neutral-400">
                  Current {metric.label.toLowerCase()}
                </p>
              </div>

              {/* Breakdown */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-neutral-900 dark:text-white flex items-center">
                  <TrendingUp className="w-5 h-5 mr-2" />
                  Breakdown
                </h3>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-neutral-50 dark:bg-neutral-700/50 rounded-lg p-3">
                    <div className="text-sm text-neutral-600 dark:text-neutral-400 mb-1">Today</div>
                    <div className="text-lg font-semibold text-neutral-900 dark:text-white">
                      {formatValue(metric.breakdown.today, metric.type)}
                    </div>
                  </div>
                  
                  <div className="bg-neutral-50 dark:bg-neutral-700/50 rounded-lg p-3">
                    <div className="text-sm text-neutral-600 dark:text-neutral-400 mb-1">This Week</div>
                    <div className="text-lg font-semibold text-neutral-900 dark:text-white">
                      {formatValue(metric.breakdown.thisWeek, metric.type)}
                    </div>
                  </div>
                  
                  <div className="bg-neutral-50 dark:bg-neutral-700/50 rounded-lg p-3">
                    <div className="text-sm text-neutral-600 dark:text-neutral-400 mb-1">This Month</div>
                    <div className="text-lg font-semibold text-neutral-900 dark:text-white">
                      {formatValue(metric.breakdown.thisMonth, metric.type)}
                    </div>
                  </div>
                  
                  <div className="bg-neutral-50 dark:bg-neutral-700/50 rounded-lg p-3">
                    <div className="text-sm text-neutral-600 dark:text-neutral-400 mb-1">All Time</div>
                    <div className="text-lg font-semibold text-neutral-900 dark:text-white">
                      {formatValue(metric.breakdown.allTime, metric.type)}
                    </div>
                  </div>
                </div>

                {metric.breakdown.best && (
                  <div className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-neutral-600 dark:text-neutral-400">Best {metric.type === 'streak' ? 'Streak' : 'Day'}</div>
                      <Award className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                    </div>
                    <div className="text-lg font-semibold text-primary-700 dark:text-primary-300">
                      {formatValue(metric.breakdown.best, metric.type)}
                    </div>
                  </div>
                )}
              </div>

              {/* Goals Progress */}
              {metric.goals && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-neutral-900 dark:text-white flex items-center">
                    <Target className="w-5 h-5 mr-2" />
                    Goals
                  </h3>
                  
                  {metric.goals.daily && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-neutral-600 dark:text-neutral-400">Daily Goal</span>
                        <span className="text-neutral-900 dark:text-white">
                          {formatValue(metric.breakdown.today, metric.type)} / {formatValue(metric.goals.daily, metric.type)}
                        </span>
                      </div>
                      <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${getProgressPercentage(metric.breakdown.today, metric.goals.daily)}%` }}
                        />
                      </div>
                    </div>
                  )}
                  
                  {metric.goals.weekly && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-neutral-600 dark:text-neutral-400">Weekly Goal</span>
                        <span className="text-neutral-900 dark:text-white">
                          {formatValue(metric.breakdown.thisWeek, metric.type)} / {formatValue(metric.goals.weekly, metric.type)}
                        </span>
                      </div>
                      <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-secondary-500 to-tertiary-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${getProgressPercentage(metric.breakdown.thisWeek, metric.goals.weekly)}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Insights */}
              {metric.insights.length > 0 && (
                <div className="space-y-3">
                  <h3 className="text-lg font-semibold text-neutral-900 dark:text-white flex items-center">
                    <Zap className="w-5 h-5 mr-2" />
                    Insights
                  </h3>
                  
                  <div className="space-y-2">
                    {metric.insights.map((insight, index) => (
                      <div
                        key={index}
                        className="flex items-start space-x-2 text-sm text-neutral-600 dark:text-neutral-400"
                      >
                        <div className="w-1.5 h-1.5 bg-primary-500 rounded-full mt-2 flex-shrink-0" />
                        <span>{insight}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-neutral-50 dark:bg-neutral-700/50 border-t border-neutral-200 dark:border-neutral-700">
              <Button
                onClick={onClose}
                className="w-full"
                variant="outline"
              >
                Close
              </Button>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}

export default MetricModal;
