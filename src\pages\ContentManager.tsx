// Content Management Panel
import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardContent, 
  Button, 
  Badge,
  DarkModeToggle,
  Breadcrumb,
  BackToTop 
} from '@/components/ui';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Download, 
  Upload, 
  Eye, 
  AlertTriangle, 
  CheckCircle,
  FileText,
  Database,
  Settings
} from 'lucide-react';
import { validateLesson, exportLessonToJSON, exportLessonToYAML } from '@/utils/contentValidation';
import { contentLoader } from '@/content';
import type { Lesson, JLPTLevel, LessonCategory } from '@/types';

export const ContentManager: React.FC = () => {
  const [lessons, setLessons] = useState<Lesson[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLevel, setSelectedLevel] = useState<JLPTLevel | 'all'>('all');
  const [selectedCategory, setSelectedCategory] = useState<LessonCategory | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [validationResults, setValidationResults] = useState<Record<string, any>>({});

  // Load all lessons
  useEffect(() => {
    const loadAllLessons = async () => {
      setLoading(true);
      try {
        const allLessons: Lesson[] = [];
        const levels: JLPTLevel[] = ['N5', 'N4', 'N3', 'N2', 'N1'];
        const categories: LessonCategory[] = ['grammar', 'vocabulary', 'kanji', 'listening', 'reading'];

        for (const level of levels) {
          for (const category of categories) {
            try {
              const categoryContent = await contentLoader.loadCategory(level, category);
              allLessons.push(...categoryContent.lessons);
            } catch (error) {
              console.warn(`No content found for ${level} ${category}`);
            }
          }
        }

        setLessons(allLessons);
        
        // Validate all lessons
        const results: Record<string, any> = {};
        allLessons.forEach(lesson => {
          results[lesson.id] = validateLesson(lesson);
        });
        setValidationResults(results);
      } catch (error) {
        console.error('Error loading lessons:', error);
      } finally {
        setLoading(false);
      }
    };

    loadAllLessons();
  }, []);

  // Filter lessons
  const filteredLessons = lessons.filter(lesson => {
    const matchesLevel = selectedLevel === 'all' || lesson.level === selectedLevel;
    const matchesCategory = selectedCategory === 'all' || lesson.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      lesson.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lesson.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      lesson.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesLevel && matchesCategory && matchesSearch;
  });

  // Export lesson
  const handleExportLesson = (lesson: Lesson, format: 'json' | 'yaml') => {
    const content = format === 'json' 
      ? exportLessonToJSON(lesson)
      : exportLessonToYAML(lesson);
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${lesson.id}.${format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Get validation status
  const getValidationStatus = (lessonId: string) => {
    const result = validationResults[lessonId];
    if (!result) return { status: 'unknown', count: 0 };
    
    if (result.errors.length > 0) {
      return { status: 'error', count: result.errors.length };
    } else if (result.warnings.length > 0) {
      return { status: 'warning', count: result.warnings.length };
    } else {
      return { status: 'valid', count: 0 };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          <div className="mb-4">
            <Breadcrumb />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                <Database className="mr-3 h-8 w-8 text-primary-600" />
                Content Manager
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Manage and validate lesson content
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <DarkModeToggle />
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Lesson
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Total Lessons</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">{lessons.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Valid</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Object.values(validationResults).filter(r => r.isValid && r.warnings.length === 0).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Warnings</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Object.values(validationResults).filter(r => r.isValid && r.warnings.length > 0).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Errors</p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {Object.values(validationResults).filter(r => !r.isValid).length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  JLPT Level
                </label>
                <select
                  value={selectedLevel}
                  onChange={(e) => setSelectedLevel(e.target.value as JLPTLevel | 'all')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Levels</option>
                  <option value="N5">N5</option>
                  <option value="N4">N4</option>
                  <option value="N3">N3</option>
                  <option value="N2">N2</option>
                  <option value="N1">N1</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value as LessonCategory | 'all')}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  <option value="grammar">Grammar</option>
                  <option value="vocabulary">Vocabulary</option>
                  <option value="kanji">Kanji</option>
                  <option value="listening">Listening</option>
                  <option value="reading">Reading</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Search
                </label>
                <input
                  type="text"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  placeholder="Search lessons..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lessons List */}
        <div className="space-y-4">
          {filteredLessons.map((lesson) => {
            const validation = getValidationStatus(lesson.id);
            
            return (
              <Card key={lesson.id} variant="interactive">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {lesson.title}
                        </h3>
                        <Badge variant={lesson.level === 'N5' ? 'success' : lesson.level === 'N1' ? 'error' : 'default'}>
                          {lesson.level}
                        </Badge>
                        <Badge variant="outline">
                          {lesson.category}
                        </Badge>
                        {validation.status === 'valid' && (
                          <Badge variant="success">
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Valid
                          </Badge>
                        )}
                        {validation.status === 'warning' && (
                          <Badge variant="warning">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            {validation.count} Warning{validation.count > 1 ? 's' : ''}
                          </Badge>
                        )}
                        {validation.status === 'error' && (
                          <Badge variant="error">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            {validation.count} Error{validation.count > 1 ? 's' : ''}
                          </Badge>
                        )}
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mb-2">
                        {lesson.description}
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                        <span>Difficulty: {lesson.difficulty}/5</span>
                        <span>Time: {lesson.estimatedTime}min</span>
                        <span>Content blocks: {lesson.content.length}</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleExportLesson(lesson, 'json')}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredLessons.length === 0 && (
          <Card>
            <CardContent className="p-12 text-center">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                No lessons found
              </h3>
              <p className="text-gray-600 dark:text-gray-300">
                Try adjusting your filters or search terms.
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      <BackToTop />
    </div>
  );
};
