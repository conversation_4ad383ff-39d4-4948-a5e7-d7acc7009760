// Content Validation Schema and Utilities
import type { Lesson, LessonContent, QuizQuestion, InteractiveContent } from '@/types';

// Validation error types
export interface ValidationError {
  field: string;
  message: string;
  severity: 'error' | 'warning';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

// Content validation functions
export const validateLesson = (lesson: any): ValidationResult => {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];

  // Required fields validation
  if (!lesson.id || typeof lesson.id !== 'string') {
    errors.push({ field: 'id', message: 'Lesson ID is required and must be a string', severity: 'error' });
  }

  if (!lesson.title || typeof lesson.title !== 'string') {
    errors.push({ field: 'title', message: 'Lesson title is required and must be a string', severity: 'error' });
  }

  if (!lesson.description || typeof lesson.description !== 'string') {
    errors.push({ field: 'description', message: 'Lesson description is required and must be a string', severity: 'error' });
  }

  if (!lesson.level || !['N5', 'N4', 'N3', 'N2', 'N1'].includes(lesson.level)) {
    errors.push({ field: 'level', message: 'Lesson level must be one of: N5, N4, N3, N2, N1', severity: 'error' });
  }

  if (!lesson.category || !['grammar', 'vocabulary', 'kanji', 'listening', 'reading'].includes(lesson.category)) {
    errors.push({ field: 'category', message: 'Lesson category must be one of: grammar, vocabulary, kanji, listening, reading', severity: 'error' });
  }

  if (typeof lesson.difficulty !== 'number' || lesson.difficulty < 1 || lesson.difficulty > 5) {
    errors.push({ field: 'difficulty', message: 'Difficulty must be a number between 1 and 5', severity: 'error' });
  }

  if (typeof lesson.estimatedTime !== 'number' || lesson.estimatedTime < 1) {
    errors.push({ field: 'estimatedTime', message: 'Estimated time must be a positive number', severity: 'error' });
  }

  // Content validation
  if (!Array.isArray(lesson.content)) {
    errors.push({ field: 'content', message: 'Lesson content must be an array', severity: 'error' });
  } else {
    lesson.content.forEach((content: any, index: number) => {
      const contentErrors = validateLessonContent(content, index);
      errors.push(...contentErrors.errors);
      warnings.push(...contentErrors.warnings);
    });
  }

  // Warnings for optional but recommended fields
  if (!lesson.tags || !Array.isArray(lesson.tags) || lesson.tags.length === 0) {
    warnings.push({ field: 'tags', message: 'Adding tags helps with lesson discoverability', severity: 'warning' });
  }

  if (!lesson.createdAt) {
    warnings.push({ field: 'createdAt', message: 'CreatedAt timestamp is recommended for tracking', severity: 'warning' });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validateLessonContent = (content: any, index: number): ValidationResult => {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];
  const prefix = `content[${index}]`;

  // Required fields
  if (!content.id || typeof content.id !== 'string') {
    errors.push({ field: `${prefix}.id`, message: 'Content ID is required and must be a string', severity: 'error' });
  }

  if (!content.type || typeof content.type !== 'string') {
    errors.push({ field: `${prefix}.type`, message: 'Content type is required and must be a string', severity: 'error' });
  }

  if (typeof content.order !== 'number') {
    errors.push({ field: `${prefix}.order`, message: 'Content order must be a number', severity: 'error' });
  }

  if (!content.data || typeof content.data !== 'object') {
    errors.push({ field: `${prefix}.data`, message: 'Content data is required and must be an object', severity: 'error' });
  }

  // Type-specific validation
  if (content.type && content.data) {
    switch (content.type) {
      case 'text':
        if (!content.data.text || typeof content.data.text !== 'string') {
          errors.push({ field: `${prefix}.data.text`, message: 'Text content requires a text field', severity: 'error' });
        }
        break;

      case 'image':
        if (!content.data.url || typeof content.data.url !== 'string') {
          errors.push({ field: `${prefix}.data.url`, message: 'Image content requires a URL field', severity: 'error' });
        }
        if (!content.data.alt || typeof content.data.alt !== 'string') {
          warnings.push({ field: `${prefix}.data.alt`, message: 'Alt text is recommended for accessibility', severity: 'warning' });
        }
        break;

      case 'audio':
        if (!content.data.url || typeof content.data.url !== 'string') {
          errors.push({ field: `${prefix}.data.url`, message: 'Audio content requires a URL field', severity: 'error' });
        }
        break;

      case 'video':
        if (!content.data.url || typeof content.data.url !== 'string') {
          errors.push({ field: `${prefix}.data.url`, message: 'Video content requires a URL field', severity: 'error' });
        }
        break;

      case 'quiz':
        if (!content.data.questions || !Array.isArray(content.data.questions)) {
          errors.push({ field: `${prefix}.data.questions`, message: 'Quiz content requires a questions array', severity: 'error' });
        } else {
          content.data.questions.forEach((question: any, qIndex: number) => {
            const questionErrors = validateQuizQuestion(question, `${prefix}.data.questions[${qIndex}]`);
            errors.push(...questionErrors.errors);
            warnings.push(...questionErrors.warnings);
          });
        }
        break;

      case 'interactive':
        if (!content.data.type || typeof content.data.type !== 'string') {
          errors.push({ field: `${prefix}.data.type`, message: 'Interactive content requires a type field', severity: 'error' });
        }
        break;

      default:
        warnings.push({ field: `${prefix}.type`, message: `Unknown content type: ${content.type}`, severity: 'warning' });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

export const validateQuizQuestion = (question: any, prefix: string): ValidationResult => {
  const errors: ValidationError[] = [];
  const warnings: ValidationError[] = [];

  if (!question.id || typeof question.id !== 'string') {
    errors.push({ field: `${prefix}.id`, message: 'Question ID is required', severity: 'error' });
  }

  if (!question.type || !['multiple-choice', 'true-false'].includes(question.type)) {
    errors.push({ field: `${prefix}.type`, message: 'Question type must be multiple-choice or true-false', severity: 'error' });
  }

  if (!question.question || typeof question.question !== 'string') {
    errors.push({ field: `${prefix}.question`, message: 'Question text is required', severity: 'error' });
  }

  if (!question.correctAnswer) {
    errors.push({ field: `${prefix}.correctAnswer`, message: 'Correct answer is required', severity: 'error' });
  }

  if (question.type === 'multiple-choice') {
    if (!question.options || !Array.isArray(question.options) || question.options.length < 2) {
      errors.push({ field: `${prefix}.options`, message: 'Multiple choice questions need at least 2 options', severity: 'error' });
    }
  }

  if (typeof question.points !== 'number' || question.points < 1) {
    warnings.push({ field: `${prefix}.points`, message: 'Question points should be a positive number', severity: 'warning' });
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
};

// Content sanitization
export const sanitizeLesson = (lesson: any): Lesson => {
  return {
    id: String(lesson.id || '').trim(),
    title: String(lesson.title || '').trim(),
    description: String(lesson.description || '').trim(),
    level: lesson.level,
    category: lesson.category,
    difficulty: Number(lesson.difficulty) || 1,
    estimatedTime: Number(lesson.estimatedTime) || 15,
    tags: Array.isArray(lesson.tags) ? lesson.tags.map(tag => String(tag).trim()) : [],
    content: Array.isArray(lesson.content) ? lesson.content : [],
    createdAt: lesson.createdAt ? new Date(lesson.createdAt) : new Date(),
    updatedAt: new Date(),
  };
};

// Content export utilities
export const exportLessonToJSON = (lesson: Lesson): string => {
  return JSON.stringify(lesson, null, 2);
};

export const exportLessonToYAML = (lesson: Lesson): string => {
  // Simple YAML export (for basic structure)
  const yamlLines: string[] = [];
  
  yamlLines.push(`id: "${lesson.id}"`);
  yamlLines.push(`title: "${lesson.title}"`);
  yamlLines.push(`description: "${lesson.description}"`);
  yamlLines.push(`level: "${lesson.level}"`);
  yamlLines.push(`category: "${lesson.category}"`);
  yamlLines.push(`difficulty: ${lesson.difficulty}`);
  yamlLines.push(`estimatedTime: ${lesson.estimatedTime}`);
  yamlLines.push(`tags:`);
  lesson.tags.forEach(tag => yamlLines.push(`  - "${tag}"`));
  yamlLines.push(`content:`);
  lesson.content.forEach((content, index) => {
    yamlLines.push(`  - id: "${content.id}"`);
    yamlLines.push(`    type: "${content.type}"`);
    yamlLines.push(`    order: ${content.order}`);
    yamlLines.push(`    data:`);
    Object.entries(content.data).forEach(([key, value]) => {
      yamlLines.push(`      ${key}: ${JSON.stringify(value)}`);
    });
  });
  
  return yamlLines.join('\n');
};
