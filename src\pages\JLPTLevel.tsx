import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  Badge,
  Progress,
} from '@/components/ui';
import { 
  BookOpen, 
  PenTool, 
  Type, 
  Headphones, 
  Play, 
  Clock, 
  Users,
  Star,
  ArrowLeft,
  ChevronRight
} from 'lucide-react';
import { JLPT_LEVEL_INFO, CATEGORY_INFO } from '@/utils';
import { contentLoader, type LevelContent } from '@/content';
import type { JLPTLevel, LessonCategory } from '@/types';

export const JLPTLevel: React.FC = () => {
  const { level } = useParams<{ level: string }>();
  const navigate = useNavigate();
  const [levelContent, setLevelContent] = useState<LevelContent | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const jlptLevel = level?.toUpperCase() as JLPTLevel;
  const levelInfo = JLPT_LEVEL_INFO[jlptLevel];

  // Load dynamic content
  useEffect(() => {
    const loadContent = async () => {
      if (!jlptLevel) return;

      setLoading(true);
      setError(null);

      try {
        const content = await contentLoader.loadLevelContent(jlptLevel);
        setLevelContent(content);
      } catch (err) {
        setError('Failed to load level content');
        console.error('Error loading level content:', err);
      } finally {
        setLoading(false);
      }
    };

    loadContent();
  }, [jlptLevel]);

  if (!levelInfo) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Level Not Found</h1>
        <Button onClick={() => navigate('/')}>Back to Dashboard</Button>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading level content...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Content</h1>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={() => navigate('/')}>Back to Dashboard</Button>
      </div>
    );
  }

  // Use dynamic content data
  const levelData = {
    totalLessons: levelContent?.info.totalLessons || 0,
    completedLessons: Math.floor((levelContent?.info.totalLessons || 0) * 0.6), // Mock completion
    estimatedHours: levelContent?.info.estimatedHours || 0,
    studentsEnrolled: 15420, // Mock data
    averageRating: 4.8, // Mock data
  };

  // Generate categories from dynamic content
  const categories = levelContent ? Object.entries(levelContent.categories).map(([key, categoryContent]) => {
    const categoryInfo = CATEGORY_INFO[key as LessonCategory];
    const totalLessons = categoryContent.info.totalLessons;
    const completed = Math.floor(totalLessons * 0.7); // Mock completion rate

    return {
      key: key as LessonCategory,
      icon: categoryInfo?.icon || BookOpen,
      lessons: totalLessons,
      completed,
      difficulty: levelContent.info.difficulty,
      name: categoryContent.info.name,
      description: categoryContent.info.description,
    };
  }) : [];

  const completionRate = Math.round((levelData.completedLessons / levelData.totalLessons) * 100);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="space-y-4">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/')}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
        
        <div className="flex items-start justify-between">
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className={`w-16 h-16 rounded-full ${levelInfo.color} text-white flex items-center justify-center font-bold text-2xl`}>
                {jlptLevel}
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">{levelContent?.info.title || levelInfo.name}</h1>
                <p className="text-xl text-gray-600">{levelContent?.info.description || levelInfo.description}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <BookOpen className="h-4 w-4" />
                <span>{levelData.totalLessons} lessons</span>
              </div>
              <div className="flex items-center space-x-1">
                <Clock className="h-4 w-4" />
                <span>~{levelData.estimatedHours} hours</span>
              </div>
              <div className="flex items-center space-x-1">
                <Users className="h-4 w-4" />
                <span>{levelData.studentsEnrolled.toLocaleString()} students</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 text-yellow-500" />
                <span>{levelData.averageRating}/5</span>
              </div>
            </div>
          </div>
          
          <Button size="lg">
            <Play className="mr-2 h-5 w-5" />
            {completionRate > 0 ? 'Continue Learning' : 'Start Learning'}
          </Button>
        </div>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Your Progress</CardTitle>
          <CardDescription>
            {levelData.completedLessons} of {levelData.totalLessons} lessons completed
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Progress value={completionRate} className="h-3" />
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                {completionRate}% Complete
              </span>
              <span className="font-medium text-gray-900">
                {levelData.totalLessons - levelData.completedLessons} lessons remaining
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Learning Categories */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-gray-900">Learning Categories</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {categories.map((category) => {
            const Icon = category.icon;
            const categoryInfo = CATEGORY_INFO[category.key];
            const categoryCompletion = Math.round((category.completed / category.lessons) * 100);
            
            return (
              <Card 
                key={category.key} 
                className="hover:shadow-md transition-shadow cursor-pointer group"
                onClick={() => navigate(`/level/${jlptLevel.toLowerCase()}/category/${category.key}`)}
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-3 bg-primary-100 rounded-lg group-hover:bg-primary-200 transition-colors">
                        <Icon className="h-6 w-6 text-primary-600" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">{categoryInfo.name}</CardTitle>
                        <CardDescription>{category.difficulty} Level</CardDescription>
                      </div>
                    </div>
                    <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-gray-600 transition-colors" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">
                        {category.completed}/{category.lessons} lessons
                      </span>
                      <Badge variant={categoryCompletion === 100 ? 'success' : 'outline'}>
                        {categoryCompletion}% Complete
                      </Badge>
                    </div>
                    <Progress value={categoryCompletion} />
                    <p className="text-sm text-gray-600">{categoryInfo.description}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Study Plan */}
      <Card>
        <CardHeader>
          <CardTitle>Recommended Study Plan</CardTitle>
          <CardDescription>
            Complete {jlptLevel} in approximately {Math.ceil(levelData.estimatedHours / 30)} weeks with 30 minutes daily study
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-600 mb-1">Week 1-2</div>
              <div className="text-sm text-gray-600">Foundation Building</div>
              <div className="text-xs text-gray-500 mt-1">Basic vocabulary & grammar</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-600 mb-1">Week 3-6</div>
              <div className="text-sm text-gray-600">Skill Development</div>
              <div className="text-xs text-gray-500 mt-1">Kanji & listening practice</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-primary-600 mb-1">Week 7-8</div>
              <div className="text-sm text-gray-600">Test Preparation</div>
              <div className="text-xs text-gray-500 mt-1">Practice tests & review</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
