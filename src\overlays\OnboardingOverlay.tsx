import React, { useEffect, useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, X, SkipForward } from 'lucide-react';
import { Button } from '@/components/ui';
import { useFirstTimeUser, type OnboardingStep } from '@/hooks/useFirstTimeUser';

interface OnboardingOverlayProps {
  isActive: boolean;
  currentStep: OnboardingStep | null;
  onNext: () => void;
  onPrevious: () => void;
  onSkip: () => void;
  onComplete: () => void;
  progress: {
    current: number;
    total: number;
    percentage: number;
  };
}

interface TooltipPosition {
  top: number;
  left: number;
  width: number;
  height: number;
}

const tooltipVariants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 10,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      damping: 25,
      stiffness: 300,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: 10,
    transition: {
      duration: 0.2,
    },
  },
};

const overlayVariants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 },
  exit: { opacity: 0 },
};

export function OnboardingOverlay({
  isActive,
  currentStep,
  onNext,
  onPrevious,
  onSkip,
  onComplete,
  progress,
}: OnboardingOverlayProps) {
  const [tooltipPosition, setTooltipPosition] = useState<TooltipPosition | null>(null);
  const [highlightPosition, setHighlightPosition] = useState<TooltipPosition | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isActive || !currentStep) {
      setTooltipPosition(null);
      setHighlightPosition(null);
      return;
    }

    const updatePosition = () => {
      const targetElement = document.querySelector(currentStep.target);
      if (!targetElement) return;

      const rect = targetElement.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      const elementPosition = {
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        width: rect.width,
        height: rect.height,
      };

      setHighlightPosition(elementPosition);

      // Calculate tooltip position based on step position preference
      let tooltipTop = elementPosition.top;
      let tooltipLeft = elementPosition.left;

      const tooltipWidth = 320; // Approximate tooltip width
      const tooltipHeight = 200; // Approximate tooltip height
      const margin = 16;

      switch (currentStep.position) {
        case 'top':
          tooltipTop = elementPosition.top - tooltipHeight - margin;
          tooltipLeft = elementPosition.left + (elementPosition.width - tooltipWidth) / 2;
          break;
        case 'bottom':
          tooltipTop = elementPosition.top + elementPosition.height + margin;
          tooltipLeft = elementPosition.left + (elementPosition.width - tooltipWidth) / 2;
          break;
        case 'left':
          tooltipTop = elementPosition.top + (elementPosition.height - tooltipHeight) / 2;
          tooltipLeft = elementPosition.left - tooltipWidth - margin;
          break;
        case 'right':
          tooltipTop = elementPosition.top + (elementPosition.height - tooltipHeight) / 2;
          tooltipLeft = elementPosition.left + elementPosition.width + margin;
          break;
      }

      // Ensure tooltip stays within viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      if (tooltipLeft < margin) {
        tooltipLeft = margin;
      } else if (tooltipLeft + tooltipWidth > viewportWidth - margin) {
        tooltipLeft = viewportWidth - tooltipWidth - margin;
      }

      if (tooltipTop < margin) {
        tooltipTop = margin;
      } else if (tooltipTop + tooltipHeight > viewportHeight - margin) {
        tooltipTop = viewportHeight - tooltipHeight - margin;
      }

      setTooltipPosition({
        top: tooltipTop,
        left: tooltipLeft,
        width: tooltipWidth,
        height: tooltipHeight,
      });
    };

    updatePosition();
    window.addEventListener('resize', updatePosition);
    window.addEventListener('scroll', updatePosition);

    return () => {
      window.removeEventListener('resize', updatePosition);
      window.removeEventListener('scroll', updatePosition);
    };
  }, [isActive, currentStep]);

  if (!isActive || !currentStep || !tooltipPosition || !highlightPosition) {
    return null;
  }

  const isFirstStep = progress.current === 1;
  const isLastStep = progress.current === progress.total;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 pointer-events-none">
        {/* Dark overlay with cutout */}
        <motion.div
          className="absolute inset-0 pointer-events-auto"
          variants={overlayVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{
            background: `
              radial-gradient(
                ellipse ${highlightPosition.width + 16}px ${highlightPosition.height + 16}px at ${
              highlightPosition.left + highlightPosition.width / 2
            }px ${highlightPosition.top + highlightPosition.height / 2}px,
                transparent 0%,
                transparent 40%,
                rgba(0, 0, 0, 0.7) 50%
              )
            `,
          }}
        />

        {/* Highlight ring */}
        <motion.div
          className="absolute border-2 border-primary-400 rounded-lg shadow-lg pointer-events-none"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          style={{
            top: highlightPosition.top - 4,
            left: highlightPosition.left - 4,
            width: highlightPosition.width + 8,
            height: highlightPosition.height + 8,
          }}
        />

        {/* Tooltip */}
        <motion.div
          ref={tooltipRef}
          className="absolute bg-white dark:bg-neutral-800 rounded-xl shadow-2xl border border-neutral-200 dark:border-neutral-700 pointer-events-auto"
          variants={tooltipVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          style={{
            top: tooltipPosition.top,
            left: tooltipPosition.left,
            width: tooltipPosition.width,
          }}
        >
          {/* Header */}
          <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-semibold text-neutral-900 dark:text-white">
                {currentStep.title}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={onComplete}
                className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            
            {/* Progress bar */}
            <div className="flex items-center space-x-2 text-xs text-neutral-600 dark:text-neutral-400">
              <span>{progress.current} of {progress.total}</span>
              <div className="flex-1 bg-neutral-200 dark:bg-neutral-700 rounded-full h-1">
                <div
                  className="bg-primary-500 h-1 rounded-full transition-all duration-300"
                  style={{ width: `${progress.percentage}%` }}
                />
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-4">
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              {currentStep.description}
            </p>

            {currentStep.action && currentStep.action !== 'none' && (
              <div className="bg-primary-50 dark:bg-primary-900/20 rounded-lg p-3 mb-4">
                <p className="text-sm text-primary-700 dark:text-primary-300">
                  💡 Try {currentStep.action === 'click' ? 'clicking' : 'hovering over'} the highlighted element!
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-4 border-t border-neutral-200 dark:border-neutral-700">
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                {!isFirstStep && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onPrevious}
                    className="flex items-center"
                  >
                    <ChevronLeft className="w-4 h-4 mr-1" />
                    Back
                  </Button>
                )}
                
                {currentStep.optional && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onSkip}
                    className="flex items-center text-neutral-600 dark:text-neutral-400"
                  >
                    <SkipForward className="w-4 h-4 mr-1" />
                    Skip
                  </Button>
                )}
              </div>

              <Button
                onClick={isLastStep ? onComplete : onNext}
                size="sm"
                className="flex items-center"
              >
                {isLastStep ? 'Finish' : 'Next'}
                {!isLastStep && <ChevronRight className="w-4 h-4 ml-1" />}
              </Button>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}

export function OnboardingProvider({ children }: { children: React.ReactNode }) {
  const {
    isActive,
    currentStepData,
    progress,
    nextStep,
    previousStep,
    skipStep,
    completeOnboarding,
  } = useFirstTimeUser();

  return (
    <>
      {children}
      <OnboardingOverlay
        isActive={isActive}
        currentStep={currentStepData}
        onNext={nextStep}
        onPrevious={previousStep}
        onSkip={skipStep}
        onComplete={completeOnboarding}
        progress={progress}
      />
    </>
  );
}

export default OnboardingOverlay;
