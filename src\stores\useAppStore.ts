import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type {
  JLPTLevel,
  UserProfile,
  UserProgress,
  Lesson,
  UserPreferences,
} from '@/types';

interface AppState {
  // User state
  user: UserProfile | null;
  isAuthenticated: boolean;

  // Current lesson state
  currentLesson: Lesson | null;
  lessonProgress: Record<string, UserProgress>;

  // UI state
  theme: 'light' | 'dark' | 'system';
  sidebarOpen: boolean;
  currentLevel: JLPTLevel;

  // Loading states
  isLoading: boolean;
  error: string | null;
}

interface AppActions {
  // User actions
  setUser: (user: UserProfile | null) => void;
  updateUserPreferences: (preferences: Partial<UserPreferences>) => void;

  // Lesson actions
  setCurrentLesson: (lesson: Lesson | null) => void;
  updateLessonProgress: (
    lessonId: string,
    progress: Partial<UserProgress>
  ) => void;
  markLessonComplete: (lessonId: string, score?: number) => void;

  // UI actions
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  setCurrentLevel: (level: JLPTLevel) => void;

  // Loading and error actions
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Utility actions
  reset: () => void;
}

type AppStore = AppState & AppActions;

const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  currentLesson: null,
  lessonProgress: {},
  theme: 'system',
  sidebarOpen: false,
  currentLevel: 'N5',
  isLoading: false,
  error: null,
};

export const useAppStore = create<AppStore>()(
  persist(
    set => ({
      ...initialState,

      // User actions
      setUser: user =>
        set({
          user,
          isAuthenticated: !!user,
          currentLevel: user?.currentLevel || 'N5',
        }),

      updateUserPreferences: preferences =>
        set(state => ({
          user: state.user
            ? {
                ...state.user,
                preferences: { ...state.user.preferences, ...preferences },
              }
            : null,
        })),

      // Lesson actions
      setCurrentLesson: lesson => set({ currentLesson: lesson }),

      updateLessonProgress: (lessonId, progress) =>
        set(state => ({
          lessonProgress: {
            ...state.lessonProgress,
            [lessonId]: {
              ...state.lessonProgress[lessonId],
              ...progress,
              lastAccessed: new Date(),
            },
          },
        })),

      markLessonComplete: (lessonId, score) =>
        set(state => {
          const existingProgress = state.lessonProgress[lessonId];
          return {
            lessonProgress: {
              ...state.lessonProgress,
              [lessonId]: {
                ...existingProgress,
                status: 'completed' as const,
                score,
                completedAt: new Date(),
                lastAccessed: new Date(),
                attempts: (existingProgress?.attempts || 0) + 1,
              },
            },
          };
        }),

      // UI actions
      setTheme: theme => set({ theme }),

      toggleSidebar: () => set(state => ({ sidebarOpen: !state.sidebarOpen })),

      setSidebarOpen: open => set({ sidebarOpen: open }),

      setCurrentLevel: level => set({ currentLevel: level }),

      // Loading and error actions
      setLoading: loading => set({ isLoading: loading }),

      setError: error => set({ error }),

      // Utility actions
      reset: () => set(initialState),
    }),
    {
      name: 'japanese-learning-app',
      partialize: state => ({
        user: state.user,
        lessonProgress: state.lessonProgress,
        theme: state.theme,
        currentLevel: state.currentLevel,
      }),
    }
  )
);

// Selectors for better performance
export const useUser = () => useAppStore(state => state.user);
export const useIsAuthenticated = () =>
  useAppStore(state => state.isAuthenticated);
export const useCurrentLesson = () => useAppStore(state => state.currentLesson);
export const useLessonProgress = () =>
  useAppStore(state => state.lessonProgress);
export const useTheme = () => useAppStore(state => state.theme);
export const useSidebarOpen = () => useAppStore(state => state.sidebarOpen);
export const useCurrentLevel = () => useAppStore(state => state.currentLevel);
export const useIsLoading = () => useAppStore(state => state.isLoading);
export const useError = () => useAppStore(state => state.error);

// Action selectors
export const useAppActions = () =>
  useAppStore(state => ({
    setUser: state.setUser,
    updateUserPreferences: state.updateUserPreferences,
    setCurrentLesson: state.setCurrentLesson,
    updateLessonProgress: state.updateLessonProgress,
    markLessonComplete: state.markLessonComplete,
    setTheme: state.setTheme,
    toggleSidebar: state.toggleSidebar,
    setSidebarOpen: state.setSidebarOpen,
    setCurrentLevel: state.setCurrentLevel,
    setLoading: state.setLoading,
    setError: state.setError,
    reset: state.reset,
  }));
