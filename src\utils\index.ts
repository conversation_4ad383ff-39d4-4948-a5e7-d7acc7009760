import type { JLPTLevel, LessonCategory } from '@/types';
import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility functions for the Japanese Learning Platform
 */

// Class name utility for merging Tailwind classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// JLPT Level utilities
export const JLPT_LEVELS: JLPTLevel[] = ['N5', 'N4', 'N3', 'N2', 'N1'];

export const JLPT_LEVEL_INFO = {
  N5: {
    name: 'N5 - Beginner',
    description: 'Basic Japanese for everyday situations',
    color: 'bg-green-500',
    textColor: 'text-green-700',
    order: 1,
  },
  N4: {
    name: 'N4 - Elementary',
    description: 'Elementary Japanese for simple conversations',
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    order: 2,
  },
  N3: {
    name: 'N3 - Intermediate',
    description: 'Intermediate Japanese for daily communication',
    color: 'bg-yellow-500',
    textColor: 'text-yellow-700',
    order: 3,
  },
  N2: {
    name: 'N2 - Upper Intermediate',
    description: 'Advanced Japanese for complex topics',
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    order: 4,
  },
  N1: {
    name: 'N1 - Advanced',
    description: 'Native-level Japanese proficiency',
    color: 'bg-red-500',
    textColor: 'text-red-700',
    order: 5,
  },
};

// Lesson Category utilities
export const LESSON_CATEGORIES: LessonCategory[] = [
  'grammar',
  'vocabulary',
  'kanji',
  'reading',
  'listening',
];

export const CATEGORY_INFO = {
  grammar: {
    name: 'Grammar',
    description: 'Japanese grammar patterns and structures',
    icon: '📝',
    color: 'bg-purple-500',
  },
  vocabulary: {
    name: 'Vocabulary',
    description: 'Essential Japanese words and phrases',
    icon: '📚',
    color: 'bg-blue-500',
  },
  kanji: {
    name: 'Kanji',
    description: 'Japanese characters and their meanings',
    icon: '漢',
    color: 'bg-red-500',
  },
  reading: {
    name: 'Reading',
    description: 'Reading comprehension and practice',
    icon: '📖',
    color: 'bg-green-500',
  },
  listening: {
    name: 'Listening',
    description: 'Audio comprehension and pronunciation',
    icon: '🎧',
    color: 'bg-orange-500',
  },
};

// Time formatting utilities
export const formatDuration = (minutes: number): string => {
  if (minutes < 60) {
    return `${minutes}m`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
};

export const formatStudyTime = (totalMinutes: number): string => {
  if (totalMinutes < 60) {
    return `${totalMinutes} minutes`;
  }
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;

  if (hours < 24) {
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours} hours`;
  }

  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days} days`;
};

// Progress calculation utilities
export const calculateProgress = (completed: number, total: number): number => {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
};

export const getProgressColor = (percentage: number): string => {
  if (percentage >= 80) return 'bg-green-500';
  if (percentage >= 60) return 'bg-yellow-500';
  if (percentage >= 40) return 'bg-orange-500';
  return 'bg-red-500';
};

// Level progression utilities
export const getNextLevel = (currentLevel: JLPTLevel): JLPTLevel | null => {
  const currentIndex = JLPT_LEVELS.indexOf(currentLevel);
  return currentIndex < JLPT_LEVELS.length - 1
    ? JLPT_LEVELS[currentIndex + 1]
    : null;
};

export const getPreviousLevel = (currentLevel: JLPTLevel): JLPTLevel | null => {
  const currentIndex = JLPT_LEVELS.indexOf(currentLevel);
  return currentIndex > 0 ? JLPT_LEVELS[currentIndex - 1] : null;
};

export const isLevelUnlocked = (
  level: JLPTLevel,
  userLevel: JLPTLevel
): boolean => {
  const levelOrder = JLPT_LEVEL_INFO[level].order;
  const userLevelOrder = JLPT_LEVEL_INFO[userLevel].order;
  return levelOrder <= userLevelOrder;
};

// String utilities
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
};

export const slugify = (text: string): string => {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
};

// Date utilities
export const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date);
};

export const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800)
    return `${Math.floor(diffInSeconds / 86400)}d ago`;

  return formatDate(date);
};

// Local storage utilities
export const storage = {
  get: <T>(key: string, defaultValue: T): T => {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch {
      return defaultValue;
    }
  },

  set: <T>(key: string, value: T): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('Failed to save to localStorage:', error);
    }
  },

  remove: (key: string): void => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Failed to remove from localStorage:', error);
    }
  },

  clear: (): void => {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Failed to clear localStorage:', error);
    }
  },
};

// Theme utilities
export const getSystemTheme = (): 'light' | 'dark' => {
  return window.matchMedia('(prefers-color-scheme: dark)').matches
    ? 'dark'
    : 'light';
};

export const applyTheme = (theme: 'light' | 'dark' | 'system'): void => {
  const root = document.documentElement;
  const actualTheme = theme === 'system' ? getSystemTheme() : theme;

  if (actualTheme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
};

// Validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const isValidPassword = (password: string): boolean => {
  return password.length >= 8;
};

// Array utilities
export const shuffle = <T>(array: T[]): T[] => {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
};

export const chunk = <T>(array: T[], size: number): T[][] => {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
};
