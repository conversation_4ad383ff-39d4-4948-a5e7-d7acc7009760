import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardT<PERSON>le,
  CardContent,
  Badge,
  Progress,
} from '@/components/ui';
import { 
  ArrowLeft, 
  ArrowRight, 
  Volume2, 
  BookOpen, 
  CheckCircle,
  RotateCcw,
  Star,
  Clock
} from 'lucide-react';
import { LessonBlockRenderer } from '@/components/LessonBlock';
import { LessonCompletion } from '@/components/LessonCompletion';
import { Breadcrumb, BackToTop } from '@/components/ui';
import { contentLoader } from '@/content';
import { useProgressStore } from '@/stores/useProgressStore';
import type { Lesson } from '@/types';

export const LessonViewer: React.FC = () => {
  const { lessonId } = useParams<{ lessonId: string }>();
  const navigate = useNavigate();
  const [lesson, setLesson] = useState<Lesson | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [quizScores, setQuizScores] = useState<Record<string, { score: number; totalPoints: number }>>({});
  const [showCompletion, setShowCompletion] = useState(false);
  const [sessionStartTime] = useState(new Date());

  // Progress tracking
  const {
    startLessonSession,
    endLessonSession,
    updateQuizScore,
    isLessonCompleted,
    getLessonProgress
  } = useProgressStore();

  // Load lesson data dynamically and start session
  useEffect(() => {
    const loadLesson = async () => {
      if (!lessonId) return;

      setLoading(true);
      setError(null);

      try {
        const lessonData = await contentLoader.loadLesson(lessonId);
        setLesson(lessonData);

        // Start lesson session for progress tracking
        startLessonSession(lessonId);
      } catch (err) {
        setError('Failed to load lesson');
        console.error('Error loading lesson:', err);
      } finally {
        setLoading(false);
      }
    };

    loadLesson();
  }, [lessonId, startLessonSession]);

  // End session on component unmount
  useEffect(() => {
    return () => {
      endLessonSession(showCompletion);
    };
  }, [endLessonSession, showCompletion]);

  // Handle quiz completion
  const handleQuizComplete = (contentId: string, score: number, totalPoints: number) => {
    setQuizScores(prev => ({
      ...prev,
      [contentId]: { score, totalPoints }
    }));

    // Update progress store
    if (lessonId) {
      updateQuizScore(lessonId, contentId, score, totalPoints);
    }
  };

  // Handle lesson completion
  const handleLessonComplete = () => {
    setShowCompletion(true);
  };

  // Calculate time spent
  const getTimeSpent = () => {
    return Math.round((new Date().getTime() - sessionStartTime.getTime()) / (1000 * 60));
  };

  // Handle completion actions
  const handleContinue = () => {
    navigate('/dashboard');
  };

  const handleRetry = () => {
    setShowCompletion(false);
    setQuizScores({});
    if (lessonId) {
      startLessonSession(lessonId);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading lesson...</p>
      </div>
    );
  }

  if (error || !lesson) {
    return (
      <div className="text-center py-12">
        <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Lesson</h1>
        <p className="text-gray-600 mb-4">{error || 'Lesson not found'}</p>
        <Button onClick={() => navigate(-1)}>Go Back</Button>
      </div>
    );
  }





  // Calculate lesson progress based on quiz scores
  const calculateProgress = () => {
    const totalQuizzes = lesson.content.filter(c => c.type === 'quiz').length;
    const completedQuizzes = Object.keys(quizScores).length;
    return totalQuizzes > 0 ? (completedQuizzes / totalQuizzes) * 100 : 100;
  };

  const progress = calculateProgress();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4">
          {/* Breadcrumb */}
          <div className="mb-4">
            <Breadcrumb />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" onClick={() => navigate(-1)}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back
              </Button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{lesson.title}</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <Badge variant="outline">{lesson.level}</Badge>
                  <span>{lesson.category}</span>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{lesson.estimatedTime} min</span>
                  </div>
                  {lesson.rating && (
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span>{lesson.rating}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Progress: {Math.round(progress)}%
              </div>
              <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                <RotateCcw className="mr-2 h-4 w-4" />
                Restart
              </Button>
            </div>
          </div>
          
          <div className="mt-4">
            <Progress value={progress} className="h-2" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {showCompletion ? (
            <LessonCompletion
              lesson={lesson}
              timeSpent={getTimeSpent()}
              quizScores={quizScores}
              onContinue={handleContinue}
              onRetry={handleRetry}
            />
          ) : (
            <>
              <LessonBlockRenderer
                content={lesson.content}
                onQuizComplete={handleQuizComplete}
              />

              {/* Complete Lesson Button */}
              <div className="mt-8 text-center">
                <Button
                  size="lg"
                  onClick={handleLessonComplete}
                  className="px-8 py-3"
                >
                  <CheckCircle className="mr-2 h-5 w-5" />
                  Complete Lesson
                </Button>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Back to Top Button */}
      <BackToTop />
    </div>
  );
};
