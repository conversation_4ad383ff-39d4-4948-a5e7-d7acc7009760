import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { ChevronRight, Home } from 'lucide-react';
import { Button } from '@/components/ui';

interface BreadcrumbItem {
  label: string;
  path: string;
  isActive?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export const Breadcrumb: React.FC<BreadcrumbProps> = ({ items, className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();

  // Auto-generate breadcrumbs from current path if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Dashboard', path: '/', isActive: location.pathname === '/' }
    ];

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;

      let label = segment;
      
      // Customize labels based on path segments
      if (segment === 'level') {
        return; // Skip 'level' segment, we'll handle the actual level
      } else if (segment.match(/^n[1-5]$/i)) {
        label = segment.toUpperCase();
      } else if (segment === 'category') {
        return; // Skip 'category' segment, we'll handle the actual category
      } else if (segment === 'lesson') {
        return; // Skip 'lesson' segment, we'll handle the actual lesson
      } else {
        // Capitalize first letter and replace hyphens with spaces
        label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
      }

      breadcrumbs.push({
        label,
        path: currentPath,
        isActive: isLast
      });
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  if (breadcrumbItems.length <= 1) {
    return null; // Don't show breadcrumbs if there's only one item
  }

  return (
    <nav className={`flex items-center space-x-1 text-sm ${className}`} aria-label="Breadcrumb">
      <Button
        variant="ghost"
        size="sm"
        className="h-auto p-1 text-gray-500 hover:text-gray-700"
        onClick={() => navigate('/')}
      >
        <Home className="h-4 w-4" />
      </Button>
      
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={item.path}>
          {index > 0 && (
            <ChevronRight className="h-4 w-4 text-gray-400" />
          )}
          
          {item.isActive ? (
            <span className="text-gray-900 font-medium px-2 py-1">
              {item.label}
            </span>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-auto p-1 text-gray-500 hover:text-gray-700"
              onClick={() => navigate(item.path)}
            >
              {item.label}
            </Button>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
};

// Hook to generate breadcrumbs for specific pages
export const useBreadcrumbs = () => {
  const location = useLocation();

  const getBreadcrumbsForPath = (customItems?: BreadcrumbItem[]): BreadcrumbItem[] => {
    if (customItems) return customItems;

    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Dashboard', path: '/' }
    ];

    // Handle specific routes
    if (pathSegments[0] === 'level' && pathSegments[1]) {
      const level = pathSegments[1].toUpperCase();
      breadcrumbs.push({
        label: `JLPT ${level}`,
        path: `/level/${pathSegments[1]}`
      });

      if (pathSegments[2] === 'category' && pathSegments[3]) {
        const categoryMap: Record<string, string> = {
          grammar: 'Grammar',
          vocabulary: 'Vocabulary',
          kanji: 'Kanji',
          reading: 'Reading',
          listening: 'Listening'
        };
        
        breadcrumbs.push({
          label: categoryMap[pathSegments[3]] || pathSegments[3],
          path: `/level/${pathSegments[1]}/category/${pathSegments[3]}`,
          isActive: true
        });
      } else {
        breadcrumbs[breadcrumbs.length - 1].isActive = true;
      }
    } else if (pathSegments[0] === 'lesson' && pathSegments[1]) {
      breadcrumbs.push({
        label: 'Lesson',
        path: `/lesson/${pathSegments[1]}`,
        isActive: true
      });
    } else if (pathSegments[0] === 'progress') {
      breadcrumbs.push({
        label: 'Progress',
        path: '/progress',
        isActive: true
      });
    } else if (pathSegments[0] === 'settings') {
      breadcrumbs.push({
        label: 'Settings',
        path: '/settings',
        isActive: true
      });
    }

    return breadcrumbs;
  };

  return { getBreadcrumbsForPath };
};
