// Text Block Component for Lesson Content
import React from 'react';
import { Card, CardContent } from '@/components/ui';
import type { TextContent } from '@/types';

interface TextBlockProps {
  content: TextContent;
  className?: string;
}

export const TextBlock: React.FC<TextBlockProps> = ({ content, className = '' }) => {
  const { text, furigana, translation, notes } = content;

  return (
    <Card className={`mb-4 ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Main Text with Furigana */}
          <div className="text-center">
            {furigana ? (
              <div className="space-y-2">
                {/* Furigana (reading) */}
                <div className="text-sm text-gray-500 font-mono tracking-wider">
                  {furigana}
                </div>
                {/* Main Japanese text */}
                <div className="text-2xl font-japanese font-medium text-gray-900">
                  {text}
                </div>
              </div>
            ) : (
              <div className="text-lg text-gray-900">
                {text}
              </div>
            )}
          </div>

          {/* Translation */}
          {translation && (
            <div className="border-t pt-4">
              <div className="text-sm text-gray-600 mb-1">Translation:</div>
              <div className="text-base text-gray-800 italic">
                {translation}
              </div>
            </div>
          )}

          {/* Notes */}
          {notes && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r">
              <div className="text-sm text-blue-800">
                <strong>Note:</strong> {notes}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
