// Lesson Block Renderer - Main component for rendering lesson content blocks
import React from 'react';
import { TextBlock } from './TextBlock';
import { AudioBlock } from './AudioBlock';
import { QuizBlock } from './QuizBlock';
import { ImageBlock } from './ImageBlock';
import { VideoBlock } from './VideoBlock';
import { InteractiveBlock } from './InteractiveBlock';
import type { LessonContent } from '@/types';

interface LessonBlockRendererProps {
  content: LessonContent[];
  onQuizComplete?: (contentId: string, score: number, totalPoints: number) => void;
  className?: string;
}

export const LessonBlockRenderer: React.FC<LessonBlockRendererProps> = ({ 
  content, 
  onQuizComplete,
  className = '' 
}) => {
  // Sort content by order
  const sortedContent = [...content].sort((a, b) => a.order - b.order);

  const renderBlock = (contentItem: LessonContent) => {
    const { id, type, data } = contentItem;

    switch (type) {
      case 'text':
        return (
          <TextBlock 
            key={id}
            content={data as any}
            className="animate-fade-in"
          />
        );

      case 'audio':
        return (
          <AudioBlock 
            key={id}
            content={data as any}
            className="animate-fade-in"
          />
        );

      case 'image':
        return (
          <ImageBlock 
            key={id}
            content={data as any}
            className="animate-fade-in"
          />
        );

      case 'video':
        return (
          <VideoBlock 
            key={id}
            content={data as any}
            className="animate-fade-in"
          />
        );

      case 'interactive':
        return (
          <InteractiveBlock 
            key={id}
            content={data as any}
            className="animate-fade-in"
          />
        );

      case 'quiz':
        return (
          <QuizBlock 
            key={id}
            content={data as any}
            onComplete={(score, totalPoints) => {
              if (onQuizComplete) {
                onQuizComplete(id, score, totalPoints);
              }
            }}
            className="animate-fade-in"
          />
        );

      default:
        return (
          <div key={id} className="mb-4 p-4 border border-yellow-300 bg-yellow-50 rounded-lg">
            <div className="text-yellow-800">
              <strong>Unknown content type:</strong> {type}
            </div>
            <div className="text-sm text-yellow-600 mt-2">
              This content type is not yet supported. Please contact support if you see this message.
            </div>
          </div>
        );
    }
  };

  if (!content || content.length === 0) {
    return (
      <div className={`text-center py-12 ${className}`}>
        <div className="text-gray-500">
          <div className="text-lg mb-2">No content available</div>
          <div className="text-sm">This lesson doesn't have any content yet.</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {sortedContent.map(renderBlock)}
    </div>
  );
};
