// Lesson Completion Component
import React, { useState, useEffect } from 'react';
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent, Button, Badge } from '@/components/ui';
import { 
  CheckCircle, 
  Clock, 
  Trophy, 
  Target, 
  ArrowRight, 
  RotateCcw,
  Star,
  TrendingUp
} from 'lucide-react';
import { useProgressStore } from '@/stores/useProgressStore';
import { LessonFeedback } from './LessonFeedback';
import type { Lesson } from '@/types';

interface LessonCompletionProps {
  lesson: Lesson;
  timeSpent: number;
  quizScores: Record<string, { score: number; totalPoints: number }>;
  onContinue?: () => void;
  onRetry?: () => void;
  nextLessonId?: string;
}

export const LessonCompletion: React.FC<LessonCompletionProps> = ({
  lesson,
  timeSpent,
  quizScores,
  onContinue,
  onRetry,
  nextLessonId,
}) => {
  const { 
    markLessonCompleted, 
    getLessonProgress, 
    getStudyStreak,
    getTotalStudyTime 
  } = useProgressStore();
  
  const [showFeedback, setShowFeedback] = useState(false);
  const [isMarkedComplete, setIsMarkedComplete] = useState(false);

  // Calculate quiz performance
  const quizResults = Object.values(quizScores);
  const totalQuizScore = quizResults.reduce((sum, quiz) => sum + quiz.score, 0);
  const totalQuizPoints = quizResults.reduce((sum, quiz) => sum + quiz.totalPoints, 0);
  const quizPercentage = totalQuizPoints > 0 ? Math.round((totalQuizScore / totalQuizPoints) * 100) : 0;
  const hasQuizzes = quizResults.length > 0;

  // Performance evaluation
  const getPerformanceLevel = () => {
    if (!hasQuizzes) return 'completed';
    if (quizPercentage >= 90) return 'excellent';
    if (quizPercentage >= 80) return 'good';
    if (quizPercentage >= 70) return 'satisfactory';
    return 'needs-improvement';
  };

  const performanceLevel = getPerformanceLevel();
  const performanceConfig = {
    excellent: {
      icon: Trophy,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100 dark:bg-yellow-900/20',
      title: 'Excellent Work!',
      message: 'You mastered this lesson with outstanding performance.',
    },
    good: {
      icon: Star,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      title: 'Great Job!',
      message: 'You did really well on this lesson.',
    },
    satisfactory: {
      icon: CheckCircle,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      title: 'Well Done!',
      message: 'You completed the lesson successfully.',
    },
    'needs-improvement': {
      icon: Target,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      title: 'Keep Practicing!',
      message: 'Consider reviewing the material and trying again.',
    },
    completed: {
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      title: 'Lesson Completed!',
      message: 'You have successfully finished this lesson.',
    },
  };

  const config = performanceConfig[performanceLevel];
  const Icon = config.icon;

  // Mark lesson as completed on mount
  useEffect(() => {
    if (!isMarkedComplete) {
      markLessonCompleted(lesson.id, timeSpent);
      setIsMarkedComplete(true);
    }
  }, [lesson.id, timeSpent, markLessonCompleted, isMarkedComplete]);

  // Get updated stats
  const currentStreak = getStudyStreak();
  const totalStudyTime = getTotalStudyTime();

  const handleMarkComplete = () => {
    setShowFeedback(true);
  };

  const handleFeedbackClose = () => {
    setShowFeedback(false);
    onContinue?.();
  };

  if (showFeedback) {
    return (
      <div className="space-y-6">
        <LessonFeedback
          lessonId={lesson.id}
          lessonTitle={lesson.title}
          onClose={handleFeedbackClose}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Main Completion Card */}
      <Card className="text-center">
        <CardContent className="p-8">
          <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full ${config.bgColor} mb-4`}>
            <Icon className={`h-8 w-8 ${config.color}`} />
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {config.title}
          </h2>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {config.message}
          </p>

          <div className="flex justify-center">
            <Badge variant="success" className="text-sm px-4 py-2">
              <CheckCircle className="w-4 h-4 mr-2" />
              Lesson Completed
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Performance Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Time Spent */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Time Spent</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {timeSpent}m
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              Est. {lesson.estimatedTime}m
            </p>
          </CardContent>
        </Card>

        {/* Quiz Performance */}
        {hasQuizzes && (
          <Card>
            <CardContent className="p-6 text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Target className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Quiz Score</span>
              </div>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {quizPercentage}%
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {totalQuizScore}/{totalQuizPoints} points
              </p>
            </CardContent>
          </Card>
        )}

        {/* Study Streak */}
        <Card>
          <CardContent className="p-6 text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <span className="text-sm font-medium text-gray-600 dark:text-gray-300">Study Streak</span>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">
              {currentStreak}
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {currentStreak === 1 ? 'day' : 'days'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quiz Breakdown */}
      {hasQuizzes && quizResults.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quiz Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(quizScores).map(([quizId, result], index) => {
                const percentage = Math.round((result.score / result.totalPoints) * 100);
                return (
                  <div key={quizId} className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-300">
                      Quiz {index + 1}
                    </span>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">
                        {result.score}/{result.totalPoints}
                      </span>
                      <Badge 
                        variant={percentage >= 80 ? 'success' : percentage >= 70 ? 'warning' : 'error'}
                        className="text-xs"
                      >
                        {percentage}%
                      </Badge>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-3 justify-center">
        {performanceLevel === 'needs-improvement' && onRetry && (
          <Button variant="outline" onClick={onRetry} className="flex items-center space-x-2">
            <RotateCcw className="h-4 w-4" />
            <span>Review Lesson</span>
          </Button>
        )}
        
        <Button onClick={handleMarkComplete} className="flex items-center space-x-2">
          <Star className="h-4 w-4" />
          <span>Rate This Lesson</span>
        </Button>

        {nextLessonId && (
          <Button onClick={onContinue} className="flex items-center space-x-2">
            <span>Continue Learning</span>
            <ArrowRight className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Encouragement Message */}
      <Card className="bg-gradient-to-r from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 border-primary-200 dark:border-primary-700">
        <CardContent className="p-6 text-center">
          <p className="text-primary-800 dark:text-primary-200 font-medium">
            {currentStreak > 1 
              ? `Amazing! You're on a ${currentStreak}-day learning streak! 🔥`
              : "Great start! Keep up the momentum! 💪"
            }
          </p>
          <p className="text-sm text-primary-600 dark:text-primary-300 mt-1">
            Total study time: {Math.floor(totalStudyTime / 60)}h {totalStudyTime % 60}m
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
