// Modern Card Component with Glassmorphism and Neumorphism
import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/utils';

const cardVariants = cva(
  'relative overflow-hidden transition-all duration-300 ease-out',
  {
    variants: {
      variant: {
        // Default clean card
        default: 'bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 shadow-md hover:shadow-lg',
        
        // Glassmorphism effect
        glass: 'bg-white/80 dark:bg-neutral-800/80 backdrop-blur-md border border-white/20 dark:border-neutral-700/30 shadow-glass hover:shadow-glass-lg',
        
        // Neumorphism effect
        neu: 'bg-neutral-100 dark:bg-neutral-800 shadow-neu hover:shadow-lg border-0',
        'neu-inset': 'bg-neutral-100 dark:bg-neutral-800 shadow-neu-inset border-0',
        
        // Gradient cards
        gradient: 'bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20 border border-primary-200/50 dark:border-primary-700/30 shadow-md hover:shadow-xl',
        'gradient-warm': 'bg-gradient-to-br from-primary-50 via-tertiary-50 to-secondary-50 dark:from-primary-900/20 dark:via-tertiary-900/20 dark:to-secondary-900/20 border-0 shadow-lg hover:shadow-xl',
        
        // Interactive cards
        interactive: 'bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 shadow-md hover:shadow-xl hover:-translate-y-1 cursor-pointer',
        'interactive-glass': 'bg-white/80 dark:bg-neutral-800/80 backdrop-blur-md border border-white/20 dark:border-neutral-700/30 shadow-glass hover:shadow-glass-lg hover:-translate-y-1 cursor-pointer',
        
        // Minimal cards
        minimal: 'bg-transparent border-0 shadow-none hover:bg-neutral-50 dark:hover:bg-neutral-800/50',
        outline: 'bg-transparent border-2 border-neutral-200 dark:border-neutral-700 shadow-none hover:border-primary-300 dark:hover:border-primary-600',
      },
      size: {
        sm: 'p-4',
        default: 'p-6',
        lg: 'p-8',
        xl: 'p-10',
      },
      rounded: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        default: 'rounded-lg',
        lg: 'rounded-xl',
        xl: 'rounded-2xl',
        '2xl': 'rounded-3xl',
        full: 'rounded-full',
      },
      glow: {
        none: '',
        subtle: 'hover:shadow-primary-500/10 hover:shadow-2xl',
        medium: 'hover:shadow-primary-500/20 hover:shadow-2xl',
        strong: 'hover:shadow-primary-500/30 hover:shadow-2xl',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      rounded: 'xl',
      glow: 'none',
    },
  }
);

const cardHeaderVariants = cva(
  'flex flex-col space-y-1.5',
  {
    variants: {
      spacing: {
        none: 'p-0',
        sm: 'pb-2',
        default: 'pb-4',
        lg: 'pb-6',
      },
    },
    defaultVariants: {
      spacing: 'default',
    },
  }
);

const cardContentVariants = cva(
  '',
  {
    variants: {
      spacing: {
        none: 'p-0',
        sm: 'pt-2',
        default: 'pt-0',
        lg: 'pt-2',
      },
    },
    defaultVariants: {
      spacing: 'default',
    },
  }
);

export interface CardModernProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
}

export interface CardModernHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardHeaderVariants> {}

export interface CardModernContentProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardContentVariants> {}

export interface CardModernTitleProps
  extends React.HTMLAttributes<HTMLHeadingElement> {}

export interface CardModernDescriptionProps
  extends React.HTMLAttributes<HTMLParagraphElement> {}

// Main Card Component
const CardModern = React.forwardRef<HTMLDivElement, CardModernProps>(
  ({ className, variant, size, rounded, glow, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardVariants({ variant, size, rounded, glow }), className)}
      {...props}
    />
  )
);
CardModern.displayName = 'CardModern';

// Card Header
const CardModernHeader = React.forwardRef<HTMLDivElement, CardModernHeaderProps>(
  ({ className, spacing, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardHeaderVariants({ spacing }), className)}
      {...props}
    />
  )
);
CardModernHeader.displayName = 'CardModernHeader';

// Card Title
const CardModernTitle = React.forwardRef<HTMLParagraphElement, CardModernTitleProps>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn(
        'text-xl font-semibold leading-none tracking-tight text-neutral-900 dark:text-neutral-100',
        className
      )}
      {...props}
    />
  )
);
CardModernTitle.displayName = 'CardModernTitle';

// Card Description
const CardModernDescription = React.forwardRef<HTMLParagraphElement, CardModernDescriptionProps>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn(
        'text-sm text-neutral-600 dark:text-neutral-400 leading-relaxed',
        className
      )}
      {...props}
    />
  )
);
CardModernDescription.displayName = 'CardModernDescription';

// Card Content
const CardModernContent = React.forwardRef<HTMLDivElement, CardModernContentProps>(
  ({ className, spacing, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardContentVariants({ spacing }), className)}
      {...props}
    />
  )
);
CardModernContent.displayName = 'CardModernContent';

// Specialized Card Components

// Stats Card for dashboard metrics
export const StatsCard: React.FC<{
  icon: React.ReactNode;
  label: string;
  value: string | number;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  className?: string;
}> = ({ icon, label, value, trend, className }) => (
  <CardModern variant="glass" glow="subtle" className={cn('group', className)}>
    <CardModernContent>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-xl bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 group-hover:scale-110 transition-transform duration-200">
            {icon}
          </div>
          <div>
            <p className="text-sm font-medium text-neutral-600 dark:text-neutral-400">{label}</p>
            <p className="text-2xl font-bold text-neutral-900 dark:text-neutral-100">{value}</p>
          </div>
        </div>
        {trend && (
          <div className={cn(
            'text-sm font-medium',
            trend.isPositive ? 'text-tertiary-600' : 'text-primary-600'
          )}>
            {trend.isPositive ? '+' : ''}{trend.value}%
          </div>
        )}
      </div>
    </CardModernContent>
  </CardModern>
);

// Progress Card for JLPT levels
export const ProgressCard: React.FC<{
  title: string;
  description: string;
  progress: number;
  level: string;
  isActive?: boolean;
  onClick?: () => void;
  className?: string;
}> = ({ title, description, progress, level, isActive, onClick, className }) => (
  <CardModern
    variant={isActive ? "gradient" : "interactive-glass"}
    glow={isActive ? "medium" : "subtle"}
    className={cn(
      'group relative overflow-hidden',
      isActive && 'ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-neutral-900',
      className
    )}
    onClick={onClick}
  >
    <CardModernHeader spacing="sm">
      <div className="flex items-center justify-between">
        <div className={cn(
          'w-12 h-12 rounded-2xl flex items-center justify-center font-bold text-lg transition-all duration-300',
          isActive 
            ? 'bg-primary-500 text-white shadow-lg' 
            : 'bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 group-hover:bg-primary-500 group-hover:text-white'
        )}>
          {level}
        </div>
        {isActive && (
          <div className="px-2 py-1 bg-primary-500 text-white text-xs font-medium rounded-full">
            Current
          </div>
        )}
      </div>
      <CardModernTitle className="text-lg">{title}</CardModernTitle>
      <CardModernDescription>{description}</CardModernDescription>
    </CardModernHeader>
    <CardModernContent>
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">Progress</span>
          <span className="text-sm font-bold text-neutral-900 dark:text-neutral-100">{progress}%</span>
        </div>
        <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2 overflow-hidden">
          <div 
            className={cn(
              'h-full rounded-full transition-all duration-1000 ease-out',
              isActive 
                ? 'bg-gradient-to-r from-primary-500 to-secondary-500' 
                : 'bg-primary-500'
            )}
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>
    </CardModernContent>
  </CardModern>
);

// Hero Card for featured content
export const HeroCard: React.FC<{
  title: string;
  description: string;
  action: React.ReactNode;
  background?: string;
  className?: string;
  children?: React.ReactNode;
}> = ({ title, description, action, background, className, children }) => (
  <CardModern
    variant="gradient-warm"
    size="lg"
    rounded="2xl"
    glow="medium"
    className={cn('relative overflow-hidden', className)}
    style={{ backgroundImage: background }}
  >
    <div className="relative z-10">
      <CardModernHeader spacing="lg">
        <CardModernTitle className="text-3xl">{title}</CardModernTitle>
        <CardModernDescription className="text-lg">{description}</CardModernDescription>
      </CardModernHeader>
      <CardModernContent>
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-4 sm:space-y-0">
          {children}
          <div className="flex-shrink-0">
            {action}
          </div>
        </div>
      </CardModernContent>
    </div>
  </CardModern>
);

export {
  CardModern,
  CardModernHeader,
  CardModernTitle,
  CardModernDescription,
  CardModernContent,
};
