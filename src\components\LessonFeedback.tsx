// Lesson Feedback Component
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>eader, CardT<PERSON>le, CardContent, Button } from '@/components/ui';
import { ThumbsUp, ThumbsDown, MessageSquare, Send, CheckCircle } from 'lucide-react';
import { useProgressStore } from '@/stores/useProgressStore';

interface LessonFeedbackProps {
  lessonId: string;
  lessonTitle: string;
  onClose?: () => void;
}

// Emoji rating system
const RATING_EMOJIS = {
  1: { emoji: '😞', label: 'Very Difficult' },
  2: { emoji: '😕', label: 'Difficult' },
  3: { emoji: '😐', label: 'Okay' },
  4: { emoji: '😊', label: 'Good' },
  5: { emoji: '😍', label: 'Excellent' },
} as const;

export const LessonFeedback: React.FC<LessonFeedbackProps> = ({
  lessonId,
  lessonTitle,
  onClose,
}) => {
  const { getLessonProgress, submitL<PERSON>onFeedback } = useProgressStore();
  const existingProgress = getLessonProgress(lessonId);
  const existingFeedback = existingProgress?.feedback;

  const [helpful, setHelpful] = useState<boolean | null>(existingFeedback?.helpful ?? null);
  const [rating, setRating] = useState<1 | 2 | 3 | 4 | 5 | null>(existingFeedback?.rating ?? null);
  const [comment, setComment] = useState(existingFeedback?.comment ?? '');
  const [showCommentBox, setShowCommentBox] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(!!existingFeedback);

  const handleSubmit = async () => {
    if (helpful === null || rating === null) return;

    setIsSubmitting(true);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    submitLessonFeedback(lessonId, {
      helpful,
      rating,
      comment: comment.trim() || undefined,
    });

    setIsSubmitted(true);
    setIsSubmitting(false);

    // Auto-close after successful submission
    setTimeout(() => {
      onClose?.();
    }, 2000);
  };

  const canSubmit = helpful !== null && rating !== null;

  if (isSubmitted && !existingFeedback) {
    return (
      <Card className="animate-fade-in">
        <CardContent className="p-6 text-center">
          <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Thank you for your feedback!
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            Your input helps us improve the learning experience.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="animate-fade-in">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MessageSquare className="h-5 w-5 text-primary-600" />
          <span>How was this lesson?</span>
        </CardTitle>
        <p className="text-sm text-gray-600 dark:text-gray-300">
          "{lessonTitle}"
        </p>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Helpful/Not Helpful */}
        <div>
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Was this lesson helpful?
          </p>
          <div className="flex space-x-3">
            <Button
              variant={helpful === true ? 'default' : 'outline'}
              size="sm"
              onClick={() => setHelpful(true)}
              className="flex items-center space-x-2"
            >
              <ThumbsUp className="h-4 w-4" />
              <span>Helpful</span>
            </Button>
            <Button
              variant={helpful === false ? 'default' : 'outline'}
              size="sm"
              onClick={() => setHelpful(false)}
              className="flex items-center space-x-2"
            >
              <ThumbsDown className="h-4 w-4" />
              <span>Not Helpful</span>
            </Button>
          </div>
        </div>

        {/* Emoji Rating */}
        <div>
          <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            How would you rate the difficulty?
          </p>
          <div className="flex space-x-2">
            {Object.entries(RATING_EMOJIS).map(([value, { emoji, label }]) => {
              const numValue = Number(value) as 1 | 2 | 3 | 4 | 5;
              const isSelected = rating === numValue;
              
              return (
                <button
                  key={value}
                  onClick={() => setRating(numValue)}
                  className={`
                    p-3 rounded-lg border-2 transition-all duration-200 hover:scale-110
                    ${isSelected 
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' 
                      : 'border-gray-200 dark:border-gray-600 hover:border-gray-300 dark:hover:border-gray-500'
                    }
                  `}
                  title={label}
                >
                  <span className="text-2xl">{emoji}</span>
                </button>
              );
            })}
          </div>
          {rating && (
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
              {RATING_EMOJIS[rating].label}
            </p>
          )}
        </div>

        {/* Optional Comment */}
        <div>
          {!showCommentBox ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCommentBox(true)}
              className="text-primary-600 hover:text-primary-700"
            >
              + Add a comment (optional)
            </Button>
          ) : (
            <div className="space-y-3">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Additional feedback (optional)
              </label>
              <textarea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="Tell us more about your experience with this lesson..."
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
                rows={3}
                maxLength={500}
              />
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {comment.length}/500 characters
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowCommentBox(false);
                    setComment('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          {onClose && (
            <Button variant="ghost" onClick={onClose}>
              Skip
            </Button>
          )}
          <Button
            onClick={handleSubmit}
            disabled={!canSubmit || isSubmitting}
            className="flex items-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <Send className="h-4 w-4" />
                <span>Submit Feedback</span>
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
