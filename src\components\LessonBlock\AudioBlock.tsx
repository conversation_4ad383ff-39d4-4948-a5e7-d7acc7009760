// Audio Block Component for Lesson Content
import React, { useState, useRef } from 'react';
import { Card, CardContent, Button } from '@/components/ui';
import { Play, Pause, Volume2, RotateCcw } from 'lucide-react';
import type { AudioContent } from '@/types';

interface AudioBlockProps {
  content: AudioContent;
  className?: string;
}

export const AudioBlock: React.FC<AudioBlockProps> = ({ content, className = '' }) => {
  const { url, transcript, duration } = content;
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [showTranscript, setShowTranscript] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleRestart = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = 0;
      setCurrentTime(0);
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <Card className={`mb-4 ${className}`}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Audio Player */}
          <div className="flex items-center justify-center">
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-full p-8 text-white">
              <Volume2 className="h-12 w-12" />
            </div>
          </div>

          {/* Audio Controls */}
          <div className="flex items-center justify-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRestart}
              className="rounded-full"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
            
            <Button
              onClick={handlePlayPause}
              className="rounded-full w-16 h-16 text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
            >
              {isPlaying ? (
                <Pause className="h-6 w-6" />
              ) : (
                <Play className="h-6 w-6 ml-1" />
              )}
            </Button>
          </div>

          {/* Progress Bar */}
          {duration && (
            <div className="space-y-2">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(currentTime / duration) * 100}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-sm text-gray-600">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>
          )}

          {/* Transcript Toggle */}
          {transcript && (
            <div className="text-center">
              <Button
                variant="ghost"
                onClick={() => setShowTranscript(!showTranscript)}
                className="text-blue-600 hover:text-blue-800"
              >
                {showTranscript ? 'Hide' : 'Show'} Transcript
              </Button>
            </div>
          )}

          {/* Transcript */}
          {transcript && showTranscript && (
            <div className="bg-gray-50 border rounded-lg p-4">
              <div className="text-sm text-gray-600 mb-2">Transcript:</div>
              <div className="text-base text-gray-800 font-japanese">
                {transcript}
              </div>
            </div>
          )}

          {/* Hidden Audio Element */}
          <audio
            ref={audioRef}
            src={url}
            onTimeUpdate={handleTimeUpdate}
            onEnded={handleEnded}
            onLoadedMetadata={() => {
              if (audioRef.current && !duration) {
                // Update duration if not provided
                setCurrentTime(0);
              }
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
};
