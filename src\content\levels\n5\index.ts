// N5 Level Content Structure
import type { LevelContent } from '@/content';
import { grammarContent } from './grammar';
import { vocabularyContent } from './vocabulary';
import { kanjiContent } from './kanji';
import { listeningContent } from './listening';

export const n5LevelContent: LevelContent = {
  info: {
    level: 'N5',
    title: 'JLPT N5 - Beginner Level',
    description: 'Master the fundamentals of Japanese language. Learn basic grammar patterns, essential vocabulary, and foundational kanji characters.',
    totalLessons: 48,
    estimatedHours: 120,
    difficulty: 'Beginner',
    skills: [
      'Basic sentence structure',
      'Present and past tense',
      'Essential vocabulary (800+ words)',
      'Basic kanji (100+ characters)',
      'Simple conversations',
      'Numbers and time',
    ],
  },
  categories: {
    grammar: grammarContent,
    vocabulary: vocabularyContent,
    kanji: kanjiContent,
    listening: listeningContent,
    reading: {
      info: {
        category: 'reading',
        name: 'Reading Comprehension',
        description: 'Practice reading simple Japanese texts and stories',
        icon: 'BookOpen',
        totalLessons: 8,
        estimatedHours: 20,
      },
      lessons: [], // Will be populated later
    },
  },
};

export default n5LevelContent;
