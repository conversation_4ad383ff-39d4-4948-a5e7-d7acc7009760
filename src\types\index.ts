// JLPT Levels
export type JLPTLevel = 'N5' | 'N4' | 'N3' | 'N2' | 'N1';

// Lesson Categories
export type LessonCategory =
  | 'grammar'
  | 'vocabulary'
  | 'kanji'
  | 'reading'
  | 'listening';

// Lesson Content Types
export type ContentType =
  | 'text'
  | 'image'
  | 'audio'
  | 'video'
  | 'interactive'
  | 'quiz';

// Base Lesson Interface
export interface Lesson {
  id: string;
  title: string;
  description: string;
  level: JLPTLevel;
  category: LessonCategory;
  difficulty: number; // 1-5 scale
  estimatedTime: number; // in minutes
  tags: string[];
  content: LessonContent[];
  prerequisites?: string[]; // lesson IDs
  createdAt: Date;
  updatedAt: Date;
}

// Lesson Content Block
export interface LessonContent {
  id: string;
  type: ContentType;
  order: number;
  data: ContentData;
}

// Content Data Union Type
export type ContentData =
  | TextContent
  | ImageContent
  | AudioContent
  | VideoContent
  | InteractiveContent
  | QuizContent;

// Specific Content Types
export interface TextContent {
  text: string;
  furigana?: string; // for Japanese text with reading aids
  translation?: string;
  notes?: string;
}

export interface ImageContent {
  url: string;
  alt: string;
  caption?: string;
}

export interface AudioContent {
  url: string;
  transcript?: string;
  duration?: number;
}

export interface VideoContent {
  url: string;
  thumbnail?: string;
  duration?: number;
  subtitles?: string;
}

export interface InteractiveContent {
  type: 'flashcard' | 'drag-drop' | 'fill-blank' | 'matching';
  data: Record<string, unknown>;
}

export interface QuizContent {
  questions: QuizQuestion[];
  passingScore?: number;
}

export interface QuizQuestion {
  id: string;
  type: 'multiple-choice' | 'true-false' | 'fill-blank' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string | string[];
  explanation?: string;
  points: number;
}

// User Progress Tracking
export interface UserProgress {
  userId: string;
  lessonId: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'mastered';
  score?: number;
  timeSpent: number; // in minutes
  lastAccessed: Date;
  completedAt?: Date;
  attempts: number;
}

// User Profile
export interface UserProfile {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  currentLevel: JLPTLevel;
  targetLevel: JLPTLevel;
  studyGoal: number; // minutes per day
  streak: number; // consecutive days
  totalStudyTime: number; // total minutes
  preferences: UserPreferences;
  createdAt: Date;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: 'en' | 'ja';
  notifications: boolean;
  autoplay: boolean;
  showFurigana: boolean;
  studyReminders: boolean;
}

// Navigation and UI Types
export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon?: string;
  children?: NavigationItem[];
}

export interface LevelOverview {
  level: JLPTLevel;
  title: string;
  description: string;
  totalLessons: number;
  completedLessons: number;
  categories: CategoryProgress[];
  estimatedHours: number;
}

export interface CategoryProgress {
  category: LessonCategory;
  total: number;
  completed: number;
  inProgress: number;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Search and Filter Types
export interface SearchFilters {
  level?: JLPTLevel[];
  category?: LessonCategory[];
  difficulty?: number[];
  tags?: string[];
  duration?: {
    min?: number;
    max?: number;
  };
}

export interface SearchResult {
  lessons: Lesson[];
  total: number;
  filters: SearchFilters;
}

// Theme and Styling
export interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
  };
  fonts: {
    sans: string;
    japanese: string;
  };
  spacing: Record<string, string>;
  borderRadius: Record<string, string>;
}
