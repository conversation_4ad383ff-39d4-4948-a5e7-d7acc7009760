// N4 Level Content Structure
import type { LevelContent } from '@/content';
import { grammarContent } from './grammar';
import { vocabularyContent } from './vocabulary';

export const n4LevelContent: LevelContent = {
  info: {
    level: 'N4',
    title: 'JLPT N4 - Elementary Level',
    description: 'Build upon N5 foundations with more complex grammar patterns and expanded vocabulary for everyday situations.',
    totalLessons: 64,
    estimatedHours: 180,
    difficulty: 'Elementary',
    skills: [
      'Complex sentence structures',
      'Te-form and its applications',
      'Expanded vocabulary (1500+ words)',
      'Intermediate kanji (300+ characters)',
      'Daily conversation skills',
      'Basic reading comprehension',
    ],
  },
  categories: {
    grammar: grammarContent,
    vocabulary: vocabularyContent,
    kanji: {
      info: {
        category: 'kanji',
        name: 'Kanji',
        description: 'Learn intermediate kanji characters and compound words',
        icon: 'Languages',
        totalLessons: 16,
        estimatedHours: 45,
      },
      lessons: [], // Will be populated later
    },
    listening: {
      info: {
        category: 'listening',
        name: 'Listening',
        description: 'Practice listening to longer conversations and announcements',
        icon: 'Headphones',
        totalLessons: 12,
        estimatedHours: 30,
      },
      lessons: [], // Will be populated later
    },
    reading: {
      info: {
        category: 'reading',
        name: 'Reading Comprehension',
        description: 'Read and understand simple texts and stories',
        icon: 'BookOpen',
        totalLessons: 10,
        estimatedHours: 25,
      },
      lessons: [], // Will be populated later
    },
  },
};

export default n4LevelContent;
